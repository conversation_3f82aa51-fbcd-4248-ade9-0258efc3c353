async function Fi2(I) {
    return `启动一个新的代理，该代理可以访问以下工具：${(await bv1(I)).map((W) => W.name).join(", ")}。当你在搜索关键词或文件，且不确定是否能在前几次尝试中找到正确匹配时，请使用Agent工具为你执行搜索。
  
  何时使用Agent工具：
  - 如果你正在搜索像"config"或"logger"这样的关键词，或者询问"哪个文件执行X？"之类的问题，强烈推荐使用Agent工具
  
  何时不使用Agent工具：
  - 如果你想读取特定的文件路径，请使用${uw.name}或${rw.name}工具而不是Agent工具，以更快地找到匹配项
  - 如果你正在搜索特定的类定义，如"class Foo"，请使用${rw.name}工具，以更快地找到匹配项
  - 如果你正在搜索特定文件或2-3个文件集内的代码，请使用${uw.name}工具而不是Agent工具，以更快地找到匹配项
  
  使用注意事项：
  1. 尽可能同时启动多个代理，以最大化性能；为此，请使用包含多个工具使用的单个消息
  2. 当代理完成任务时，它将向你返回一条消息。代理返回的结果对用户不可见。要向用户显示结果，你应该向用户发送一条文本消息，简明扼要地总结结果。
  3. 每次代理调用都是无状态的。你将无法向代理发送额外的消息，代理也无法在其最终报告之外与你通信。因此，你的提示应该包含详细的任务描述，让代理能够自主执行，并且你应该明确指定代理应该在其最终且唯一的消息中向你返回什么信息。
  4. 代理的输出通常应该被信任${
      I === "bypassPermissions"
        ? ""
        : `
  5. 重要提示：代理不能使用${c9.name}、${wI.name}、${VI.name}、${bW.name}，所以不能修改文件。如果你想使用这些工具，请直接使用它们，而不是通过代理。`
    }`;
  }
