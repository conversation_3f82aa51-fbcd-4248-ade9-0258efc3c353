async function cv1({ permissionMode: I }) {
    return `
  - 批量执行工具，可在单个请求中运行多个工具调用
  - 工具在可能的情况下并行执行，否则串行执行
  - 接受工具调用列表（工具名称和输入对）
  - 返回所有调用的收集结果
  - 当你需要一次运行多个独立的工具操作时使用此工具 -- 它非常适合加速你的工作流程，减少上下文使用和延迟
  - 每个工具都将遵守其自身的权限和验证规则
  - 工具的输出不会显示给用户；要回答用户的查询，你必须在工具调用完成后发送一条包含结果的消息，否则用户将看不到结果
  
  可用工具：
  ${(
    await Promise.all(
      (await $c5()).map(
        async (Z) => `工具：${Z.name}
  参数：${Rc5(Z.inputSchema)}
  用法：${await Z.prompt({ permissionMode: I })}`,
      ),
    )
  ).join(`
  ---`)}
  
  使用示例：
  {
    "invocations": [
      {
        "tool_name": "${c9.name}",
        "input": {
          "command": "git blame src/foo.ts"
        }
      },
      {
        "tool_name": "${rw.name}",
        "input": {
          "pattern": "**/*.ts"
        }
      },
      {
        "tool_name": "${uX.name}",
        "input": {
          "pattern": "function",
          "include": "*.ts"
        }
      }
    ]
  }
  `;
  }
