async function Yz5(I) {
    try {
      let Z = await fV({
          systemPrompt: [
            "根据此错误报告生成简洁、技术性的GitHub问题标题（最多80个字符）。标题应：",
            "- 具体且描述实际问题",
            "- 使用适合软件问题的技术术语",
            '- 对于错误消息，提取关键错误（例如，"缺少工具结果块"而不是完整消息）',
            '- 以名词或动词开头（不是"错误："或"问题："）',
            "- 直接明了，让开发人员理解问题",
            '- 如果无法确定明确的问题，使用"错误报告：[简短描述]"',
          ],
          userPrompt: I,
          isNonInteractiveSession: !1,
        }),
        G =
          Z.message.content[0]?.type === "text"
            ? Z.message.content[0].text
            : "错误报告";
      if (G.startsWith(mw)) return j$2(I);
      return G;
    } catch (Z) {
      return n1(Z instanceof Error ? Z : new Error(String(Z))), j$2(I);
    }
  }
