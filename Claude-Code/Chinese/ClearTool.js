var Pz5 = {
    type: "local",
    name: "clear",
    description: "清除对话历史并释放上下文",
    isEnabled: !0,
    isHidden: !1,
    async call(I, Z) {
      return _91(Z), "";
    },
    userFacingName() {
      return "clear";
    },
  },
  ZR2 = Pz5;
function GR2(I) {
  if (!I || I.trim() === "")
    return `你的任务是创建迄今为止对话的详细摘要，特别注意用户的明确请求和你之前的操作。
此摘要应该全面捕捉技术细节、代码模式和架构决策，这些对于在不丢失上下文的情况下继续开发工作至关重要。

在提供最终摘要之前，请用<analysis>标签包装你的分析，以组织你的思路并确保你已涵盖所有必要的要点。在你的分析过程中：

1. 按时间顺序分析对话的每条消息和每个部分。对于每个部分，彻底识别：
   - 用户的明确请求和意图
   - 你解决用户请求的方法
   - 关键决策、技术概念和代码模式
   - 特定细节，如文件名、完整代码片段、函数签名、文件编辑等
2. 仔细检查技术准确性和完整性，彻底解决每个必需的元素。

你的摘要应包括以下部分：

1. 主要请求和意图：详细捕捉用户的所有明确请求和意图
2. 关键技术概念：列出所有讨论的重要技术概念、技术和框架。
3. 文件和代码部分：列举检查、修改或创建的特定文件和代码部分。特别注意最近的消息，并在适用的情况下包括完整的代码片段，并包括为什么这个文件读取或编辑很重要的摘要。
4. 问题解决：记录已解决的问题和任何正在进行的故障排除工作。
5. 待处理任务：概述你被明确要求处理的任何待处理任务。
6. 当前工作：详细描述在此摘要请求之前正在进行的工作，特别注意用户和助手最近的消息。在适用的情况下包括文件名和代码片段。
7. 可选下一步：列出与你最近正在做的工作相关的下一步。重要：确保这一步直接符合用户的明确请求，以及你在此摘要请求之前正在处理的任务。如果你的最后一个任务已经结束，那么只有在它们明确符合用户请求的情况下才列出下一步。未经用户确认，不要开始处理切线请求。
                   如果有下一步，请包括最近对话中的直接引用，准确显示你正在处理的任务以及你停止的地方。这应该是逐字的，以确保任务解释没有偏差。

以下是你的输出应该如何结构化的示例：

<example>
<analysis>
[你的思考过程，确保所有要点都得到彻底和准确的覆盖]
</analysis>

<summary>
1. 主要请求和意图：
   [详细描述]

2. 关键技术概念：
   - [概念1]
   - [概念2]
   - [...]

3. 文件和代码部分：
   - [文件名1]
      - [为什么这个文件很重要的摘要]
      - [对这个文件所做的更改摘要，如果有的话]
      - [重要代码片段]
   - [文件名2]
      - [重要代码片段]
   - [...]

4. 问题解决：
   [已解决问题和正在进行的故障排除的描述]

5. 待处理任务：
   - [任务1]
   - [任务2]
   - [...]

6. 当前工作：
   [当前工作的精确描述]

7. 可选下一步：
   [可选的下一步]

</summary>
</example>

请根据迄今为止的对话提供你的摘要，遵循这个结构并确保你的回应精确和全面。

在包含的上下文中可能提供了额外的摘要指示。如果是这样，请记住在创建上述摘要时遵循这些指示。指示示例包括：
<example>
## 简洁指示
在总结对话时，专注于typescript代码更改，并记住你犯的错误以及你如何修复它们。
</example>

<example>
# 摘要指示
当你使用简洁模式时 - 请专注于测试输出和代码更改。逐字包括文件读取。
</example>`;
  return `你的任务是创建迄今为止对话的详细摘要，特别注意用户的明确请求和你之前的操作。
此摘要应该全面捕捉技术细节、代码模式和架构决策，这些对于在不丢失上下文的情况下继续开发工作至关重要。

在提供最终摘要之前，请用<analysis>标签包装你的分析，以组织你的思路并确保你已涵盖所有必要的要点。在你的分析过程中：

1. 按时间顺序分析对话的每条消息和每个部分。对于每个部分，彻底识别：
   - 用户的明确请求和意图
   - 你解决用户请求的方法
   - 关键决策、技术概念和代码模式
   - 特定细节，如文件名、完整代码片段、函数签名、文件编辑等
2. 仔细检查技术准确性和完整性，彻底解决每个必需的元素。

你的摘要应包括以下部分：

1. 主要请求和意图：详细捕捉用户的所有明确请求和意图
2. 关键技术概念：列出所有讨论的重要技术概念、技术和框架。
3. 文件和代码部分：列举检查、修改或创建的特定文件和代码部分。特别注意最近的消息，并在适用的情况下包括完整的代码片段，并包括为什么这个文件读取或编辑很重要的摘要。
4. 问题解决：记录已解决的问题和任何正在进行的故障排除工作。
5. 待处理任务：概述你被明确要求处理的任何待处理任务。
6. 当前工作：详细描述在此摘要请求之前正在进行的工作，特别注意用户和助手最近的消息。在适用的情况下包括文件名和代码片段。
7. 可选下一步：列出与你最近正在做的工作相关的下一步。重要：确保这一步直接符合用户的明确请求，以及你在此摘要请求之前正在处理的任务。如果你的最后一个任务已经结束，那么只有在它们明确符合用户请求的情况下才列出下一步。未经用户确认，不要开始处理切线请求。
                   如果有下一步，请包括最近对话中的直接引用，准确显示你正在处理的任务以及你停止的地方。这应该是逐字的，以确保任务解释没有偏差。

以下是你的输出应该如何结构化的示例：

<example>
<analysis>
[你的思考过程，确保所有要点都得到彻底和准确的覆盖]
</analysis>

<summary>
1. 主要请求和意图：
   [详细描述]

2. 关键技术概念：
   - [概念1]
   - [概念2]
   - [...]

3. 文件和代码部分：
   - [文件名1]
      - [为什么这个文件很重要的摘要]
      - [对这个文件所做的更改摘要，如果有的话]
      - [重要代码片段]
   - [文件名2]
      - [重要代码片段]
   - [...]

4. 问题解决：
   [已解决问题和正在进行的故障排除的描述]

5. 待处理任务：
   - [任务1]
   - [任务2]
   - [...]

6. 当前工作：
   [当前工作的精确描述]

7. 可选下一步：
   [可选的下一步]

</summary>
</example>

请根据迄今为止的对话提供你的摘要，遵循这个结构并确保你的回应精确和全面。

在包含的上下文中可能提供了额外的摘要指示。如果是这样，请记住在创建上述摘要时遵循这些指示。指示示例包括：
<example>
## 简洁指示
在总结对话时，专注于typescript代码更改，并记住你犯的错误以及你如何修复它们。
</example>

<example>
# 摘要指示
当你使用简洁模式时 - 请专注于测试输出和代码更改。逐字包括文件读取。
</example>


额外指示：
${I}`;
}
function WR2(I, Z) {
  let G = `此会话是从之前因上下文不足而中断的对话继续的。对话摘要如下：
${I}。`;
  if (Z)
    return `${G}
请从我们停止的地方继续对话，不要向用户提出任何进一步的问题。继续处理你被要求处理的最后一个任务。`;
  return G;
}
function Lz5(I) {
  if (
    I?.type === "assistant" &&
    "usage" in I.message &&
    !(
      I.message.content[0]?.type === "text" &&
      D91.has(I.message.content[0].text)
    ) &&
    I.message.model !== "<synthetic>"
  )
    return I.message.usage;
  return;
}
