var Ec2 = `这是一个用于编辑文件的工具。对于移动或重命名文件，你通常应该使用带有'mv'命令的Bash工具。对于较大的编辑，使用Write工具覆盖文件。对于Jupyter笔记本（.ipynb文件），请使用${bW.name}。

使用此工具前：

1. 使用View工具了解文件的内容和上下文

2. 验证目录路径是否正确（仅适用于创建新文件时）：
   - 使用LS工具验证父目录是否存在并且是正确的位置

要进行文件编辑，请提供以下内容：
1. file_path：要修改的文件的绝对路径（必须是绝对路径，而不是相对路径）
2. old_string：要替换的文本（必须与文件内容完全匹配，包括所有空格和缩进）
3. new_string：用于替换old_string的编辑文本
4. expected_replacements：你期望进行的替换次数。如果未指定，默认为1。

默认情况下，该工具将在指定文件中将old_string的一个出现替换为new_string。如果你想替换多个出现，请提供expected_replacements参数，指定你期望的确切出现次数。

使用此工具的关键要求：

1. 唯一性（当未指定expected_replacements时）：old_string必须唯一标识你想要更改的特定实例。这意味着：
   - 在更改点之前包含至少3-5行上下文
   - 在更改点之后包含至少3-5行上下文
   - 包含所有空格、缩进和周围代码，与文件中显示的完全一致

2. 预期匹配：如果你想替换多个实例：
   - 使用expected_replacements参数，指定你期望替换的确切出现次数
   - 这将用new_string替换old_string的所有出现
   - 如果实际匹配数量不等于expected_replacements，编辑将失败
   - 这是防止意外替换的安全功能

3. 验证：在使用此工具之前：
   - 检查目标文本在文件中存在多少个实例
   - 如果存在多个实例，则：
     a) 收集足够的上下文以唯一标识每个实例并进行单独调用，或
     b) 使用expected_replacements参数，指定你期望替换的实例的确切数量

警告：如果你不遵循这些要求：
   - 如果old_string匹配多个位置且未指定expected_replacements，工具将失败
   - 如果指定了expected_replacements但匹配数量不等于该值，工具将失败
   - 如果old_string不完全匹配（包括空格），工具将失败
   - 如果你不验证匹配计数，可能会更改意外的实例

进行编辑时：
   - 确保编辑结果是惯用的、正确的代码
   - 不要使代码处于损坏状态
   - 始终使用绝对文件路径（以/开头）

如果你想创建新文件，请使用：
   - 新文件路径，如果需要，包括目录名
   - 空的old_string
   - 新文件的内容作为new_string

记住：当对同一文件连续进行多个文件编辑时，你应该优先在单个消息中使用多次调用此工具发送所有编辑，而不是在多个消息中每个只有一次调用。
`;
