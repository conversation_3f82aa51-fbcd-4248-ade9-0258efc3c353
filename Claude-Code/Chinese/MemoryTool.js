function Xn2(I) {
    return `你被要求在${I}的记忆文件中添加记忆或更新记忆。
  
  请遵循以下指导原则：
  - 如果输入是对现有记忆的更新，请编辑或替换现有条目
  - 不要对记忆进行详细说明或添加不必要的评论
  - 保留文件的现有结构并自然地整合新记忆。如果文件为空，只需将新记忆作为项目符号条目添加，不要添加任何标题。
  - 重要提示：你的回应必须是FileWriteTool的单个工具使用`;
  }
  function I31(I) {
    let Z = C5();
    if (I === "ExperimentalUltraClaudeMd") return Xd1;
    switch (I) {
      case "User":
        return Xd1;
      case "Local":
        return Vg1(Z, "CLAUDE.local.md");
      case "Project":
        return Vg1(Z, "CLAUDE.md");
      case "ExperimentalUltraClaudeMd":
        return Vg1(Ni5(), ".claude", "ULTRACLAUDE.md");
    }
  }
  async function ii2(<PERSON>, <PERSON>, G = "User") {
    let W = I31(G);
    if (G === "Local" && !Bg1(W)) s51(W);
    Z.addNotification?.(
      { text: `保存${Ih(G)}记忆…` },
      { timeoutMs: 30000 },
    ),
      x1("tengu_add_memory_start", {}),
      Ri5();
    let B = eu(W);
    if (!Bg1(Hn2(W)))
      try {
        Ui5(Hn2(W), { recursive: !0 });
      } catch (D) {
        n1(D instanceof Error ? D : new Error(String(D)));
      }
    let V = [wI],
      w = Q5({
        content: `要添加/更新的记忆：
  \`\`\`
  ${I}
  \`\`\`
  
  现有记忆文件内容：
  \`\`\`
  ${B || "[空文件]"}
  \`\`\``,
      }),
      Y = await Gv([w], [Xn2(W)], 0, V, Z.abortController.signal, {
        permissionMode: "default",
        model: Z.options.slowAndCapableModel,
        prependCLISysprompt: !0,
        toolChoice: { name: wI.name, type: "tool" },
        isNonInteractiveSession: Z.options.isNonInteractiveSession,
      }),
      X = Y.message.content.find((D) => D.type === "tool_use");
    if (!X) {
      n1(new Error("在响应中未找到工具使用")),
        Z.addNotification?.({
          text: "保存记忆失败：在响应中未找到工具使用",
          color: "error",
        });
      return;
    }
    let H = eZ([
      await h_(
        q61(X, new Set(), Y, (D, K) => $i5(D, K, W), {
          options: Z.options,
          abortController: Z.abortController,
          readFileTimestamps: {
            [W]: Bg1(W) ? qi5(W).mtime.getTime() + 1 : Date.now(),
          },
          userProvidedHosts: Z.userProvidedHosts,
          setToolJSX: Z.setToolJSX,
          getToolPermissionContext: Z.getToolPermissionContext,
        }),
      ),
    ])[0];
    if (
      H.type === "user" &&
      H.message.content[0].type === "tool_result" &&
      H.message.content[0].is_error
    )
      throw (
        (x1("tengu_add_memory_failure", {}),
        new Error(H.message.content[0].content))
      );
    let J = eu(W);
    if (
      (x1("tengu_add_memory_success", {}),
      nw({
        filePath: W,
        fileContents: B,
        oldStr: B,
        newStr: J,
        ignoreWhitespace: !0,
      }).length > 0)
    )
      Z.addNotification?.(
        { jsx: wg1.createElement(vj2, { memoryType: G, memoryPath: W }) },
        { timeoutMs: 1e4 },
      );
    else Z.addNotification?.({ text: `${Ih(G)}记忆未做任何更改` });
  }
  async function $i5(I, Z, G) {
    if (I !== wI) return { result: !1, message: "使用了错误的工具" };
    let { file_path: W } = wI.inputSchema.parse(Z);
    if (W !== G)
      return { result: !1, message: `必须使用正确的记忆文件路径：${G}` };
    return { result: !0, updatedInput: Z };
  }
  
