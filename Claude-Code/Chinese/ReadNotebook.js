var yL1 = "ReadNotebook",
  Kd5 = 2000,
  Cd5 = 2000,
  Cg2 = "从本地文件系统读取文件。",
  Fg2 = `从本地文件系统读取文件。你可以使用此工具直接访问任何文件。
假设此工具能够读取机器上的所有文件。如果用户提供文件路径，假设该路径有效。读取不存在的文件是可以的；将返回错误。

用法：
- file_path参数必须是绝对路径，而不是相对路径
- 默认情况下，它从文件开头开始读取最多${Kd5}行
- 你可以选择指定行偏移量和限制（特别适用于长文件），但建议不提供这些参数以读取整个文件
- 任何长度超过${Cd5}个字符的行将被截断
- 结果使用cat -n格式返回，行号从1开始
- 此工具允许${S2}查看图像（如PNG、JPG等）。读取图像文件时，内容以视觉方式呈现，因为${S2}是多模态LLM。
- 对于Jupyter笔记本（.ipynb文件），请使用${yL1}
- 读取多个文件时，你必须使用${jw}工具一次性读取所有文件`;
