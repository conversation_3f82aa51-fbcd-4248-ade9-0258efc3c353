# The rise of specialty models: 6 predictions for AI in 2025

**Author:** <PERSON>  
**Date:** February 7, 2025

With DeepSeek and open source AIs closing the gap, concerns raised about large language model (LLM) commoditization, advances in LLM reasoning, and questions about the future of the scaling laws, 2025 is shaping up to be a tumultuous year in AI. And it's only February 😉.

Based on what we're seeing in the market and our own work in the field, here are six predictions for where AI is headed: 

## **_1\. The LLM moat shrinks and focus shifts to applications_**

Llama and DeepSeek are approaching the capabilities of the closed frontier models, and are of course available for free. Short of an algorithmic breakthrough that is not duplicable, this gap will continue to close, as open source providers have access to similar training data and sufficient compute (DeepSeek showing the way to greater efficiency). This will inevitably drive down the cost of using models. Progress toward commoditizing intelligence will overall be a great thing for technology and humanity (particularly as those AIs are built into applications that can mitigate concerns about alignment).  
  
The net effect on the overall industry is going to shift value up the stack: AI-enabled applications get better and more valuable, while the underlying models get cheaper. But the winning applications will not be the currently popular thin wrappers on top of existing frontier models, because they too will commoditize. Rather it is the deeper AI applications that bring model post-training, advanced agency & tooling, retrieval augmented generation (RAG), etc. to bear to deliver state of the art services. 

## **_2\. Open source LLMs drive model proliferation and specialization in the enterprise_** 

Given the fact that businesses possess the lion's share of data, and ultimately want to _own_ models that are experts within their domains, we anticipate an increasing investment in the _specialization_ of LLMs within industry verticals_.  
_  
By specialization, I mean shifting investment from the pre-training of today's frontier models to the fine tuning/distillation of those general-purpose models to better perform within a particular discipline. While general models were getting better faster, specialization did not make sense, because the Nth + 1 general-purpose model would surpass any post-training on the Nth model. But as the curve inevitably flattens, we anticipate a proliferation of ever more specialized models, much as we humans specialize at university and in our careers. At any fixed level of investment, specialization will always yield greater competence in a particular subject area. And smaller, more specialized models are faster and cheaper at inference.   
  
We expect open source models will enjoy an advantage here, as they more clearly allow a business with a corpus of proprietary training data to own the specialized models they post-train. Presumably this is why OpenAI is now contemplating open source. But I would also expect the closed model providers to enable their larger customers to craft specialized models that those customers own, even if still hosted by the model provider.  
  
Note that these specialized models will _not_ be commodities. Rather they will be proprietary experts in particular areas of human endeavor because they are trained on the most advanced proprietary data industry has to offer. No doubt this transition will take time — it took years for businesses to implement ecommerce — but I am confident that for enterprise AI, the proliferation of ever more specialized AI models primarily based on open source is inevitable. 

## 3\. **_Coding AIs increase demand for software engineers_**

No doubt coding AIs are already making human programmers well more productive, and that some cost-driven organizations use this to reduce staff. At the same time, we remain convinced that making quality software far more affordable and predictable will _increase_ overall demand (following Jevon's paradox). Enlightened CTOs and VPs of engineering will seize this opportunity to deliver on their backlog of feature requests and eliminate technical debt. And AIs will create demand for new software—agents, for example, will want to orchestrate other agents, and ML can make software more adaptive and robust.

In our view, this increased demand for software will drive the need for more software engineers. Human ingenuity is still essential to crafting great software. Granted, AIs can crank out small "0 to 1" applications from scratch by themselves. But for the vast majority of enterprise software, a basic natural language description would prove far insufficient to yield a usable implementation. Instead, the detailed, comprehensive specification of how the software should behave is realized within the code itself, which often evolves over years.

Recognize also that there is far more to software engineering than writing new code from scratch. Instead, engineers spend their time gathering requirements, designing enhancements, and planning refactors or migrations. The sophisticated reasoning driving this longer-term software lifecycle is out of reach for today's AIs. So while we are seeing AIs make rapid progress on coding benchmarks like SWE Bench, with the best models now at well over 50% success, these programming tasks are inherently incremental steps in the longer-term human guided evolution of software.

As a result, we would bet against Sam Altman's claims that there will be a [one person billion dollar software startup](https://every.to/napkin-math/the-one-person-billion-dollar-company), or that software engineering as a profession will be on the decline. Indeed, [as we have remarked elsewhere](https://www.augmentcode.com/blog/how-coding-ai-will-support-large-scale-software-engineering), organizations that can predictably achieve all of their software aspirations will be able to grow their businesses and then choose to invest more in human software engineers augmented by AIs.

## **_4\. Retrieval Augmented Generation (RAG) trumps fine tuning_**

Early efforts at delivering contextual knowledge to LLMs focused on fine tuning (e.g., GitHub Copilot training on customer code). The downsides of this approach quickly became apparent: Training large models is expensive, and for the AI to stay current, you would have to fine tune on every change (for software, on every check-in and every time you pull a branch). And there is always the worry of leaking intellectual property — unfortunately, you need to check the fine print when using AI assistants as many of them insist on the right to retain and learn from your confidential data! (Augment, of course, never trains on your software, unless you explicitly give us approval to do so, and you bless our moving you to a special tenant that we reserve for our open source developers.)  
  
Why not instead simply pass your codebase as context to the LLM? Long context is also expensive: with today's LLM architectures, computational cost grows _with the square of the context length!_ While there are a number of research efforts in the works, at least in the near term, there is no way it will make sense to pass a large dataset, such as a 10m or 100m line monorepo, as context.  
  
The answer of course is RAG. RAG can [adjust in real time to precisely what you're working on, as well as address security concerns](https://www.augmentcode.com/blog/a-real-time-index-for-your-codebase-secure-personal-scalable) — that the AI you're using is informed only by the information that you have access to. But for a developer AI to behave as if it has your entire codebase as context requires _deeply intelligent, realtime RAG_, which is why Augment has put so much of our effort into getting this right, and why we are today _the_ choice for larger repositories and more sophisticated software.  

## **_5\. AI-augmented software engineers will toggle between programming and meta-programming_** 

Two different paradigms for leveraging AI in programming are currently evolving. Most developers are starting with the approach that GitHub Copilot pioneered, and others like Augment and Cursor have improved upon: that the engineer still directly manipulates the program text, but the AI accelerates the work with code completions, or agentic anticipation of future actions (see Augment's [Next Edit](https://www.augmentcode.com/#Edits), which works across your entire repository).  
  
But many of Augment's developers are moving to more of a _meta-programming_ model in which they describe the behavior they want in chat, and the AI then generates the necessary code. (Bret Taylor describes this approach and more in a recent [blog](https://backchannel.org/blog/autonomous-software).) We anticipate much more of this approach as agents manage ever more of the programming details.  
  
Of course, the two approaches are not mutually exclusive — developers are free to shift back and forth, but be forewarned that not all AIs provide Augment's consistently thorough understanding of your software across the two paradigms. 

## **_6\. The rise of AI agents for software engineering_**

At Augment, we set out to reimagine software engineering as a deep collaboration between human and machine intelligence. In our vision, AI will take on ever more of the tedious, mundane work, freeing up humans to think more creatively and aspirationally about how the software should evolve. With appropriate human oversight, AIs from Augment and our competitors will take on ever higher level tasks in 2025: 

* Generate ever more substantive software components from natural language specifications
* Produce and expand test suites to fill gaps, resolve issues and prevent them from reoccurring
* Perform a root cause analysis (RCA) after an outage, perhaps waking up in the middle of the night before the on-call engineer to help orient them to what may have happened
* Execute code to prove out correctness and other properties
* Periodically search for security vulnerabilities, superseded libraries, no longer used code
* Produce and update documentation
* Accelerate migrations and complex refactors

And so on. At Augment, we very much believe in building such agentic workflows bottom up rather than top down — to have AI agents take on incrementally higher-level tasks, but with humans overseeing and, when necessary, guiding the process. Contextual knowledge of your codebase (see RAG discussion above) will prove ever _more essential_ as agents take on larger, more sophisticated workflows.  
  
It is very clear from the above that AIs for software engineering have a very high ceiling indeed — that it is entirely future innovations rather than the current state of the art that will determine the ultimate long-term winners in this market. Thrilled for the road ahead!

---

**About the Author:**  
Prior to Augment Code, "Dietz" served as CEO of Pure Storage. Under his leadership, Pure grew from 0 to $1B+ in revenue, 15 to thousands of employees, and had a successful IPO. A 4x entrepreneur, he earned a Ph.D. in Computer Science with an emphasis on Machine Learning from Carnegie Mellon University.
