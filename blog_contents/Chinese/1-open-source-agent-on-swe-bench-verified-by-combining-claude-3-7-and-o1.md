# 通过结合Claude 3.7和O1成为SWE-Bench验证榜单上排名第一的开源代理

**作者：** Colin Flaherty, Tong<PERSON>i Chen  
**日期：** 2025年3月31日

[**_我们开源了如何在SWE-bench验证榜单上获得第一名的方法 - 点击这里查看。_**](https://github.com/augmentcode/augment-swebench-agent)  
  
在过去几个月中，我们一直在跟踪并针对SWE-bench（代理代码基准测试的行业标准）进行优化。很明显，我们现在正处于代理时代，代理在真实代码库上准确执行的能力将变得越来越重要。今天，我们自豪地宣布，**我们在SWE-bench验证榜单上取得了65.4%的成功率。** 我们在下文提供了技术细节，并已开源了我们达到榜单顶部的方法。

**查看我们的开源仓库** [**点击这里**](https://github.com/augmentcode/augment-swebench-agent)。它非常简单，实现了整个SWE-bench流程的端到端实现（代理在Docker容器中运行、集成和评估候选解决方案）。

在Augment，我们的使命是为专业软件工程师及其团队构建最佳的AI平台。我们结合使用闭源和微调的开源模型，以提供最佳的产品体验。我们对编码体验的每个部分的模型进行严格测试、调整和优化，为每次交互选择最佳模型。这意味着工程团队可以利用AI的力量，[而无需了解](https://www.augmentcode.com/blog/ai-model-pickers-are-a-design-failure-not-a-feature)哪个模型今天能做什么（明天又会全部改变）。[立即注册Augment](https://www.augmentcode.com/)，在您选择的IDE（VSCode、JetBrains和Neovim）中获取强大的AI编码支持。

## 我们方法的总结

为了在我们首次提交SWE-bench时达到65.4%的成功率，我们将Claude Sonnet 3.7作为核心驱动，同时使用OpenAI的o1作为集成器。我们暂时没有使用自己的模型，而是使用现成的模型构建了一个强大的开源基准代理。

由于Anthropic的模型目前在代码方面处于最先进水平，我们使用Claude Sonnet 3.7作为代理的核心驱动，并从[Anthropic自己关于SWE-bench的博客文章](https://www.anthropic.com/news/claude-3-7-sonnet)中分叉了我们的代理系统架构。我们实现中的主要区别包括弄清楚他们未公开的"规划"工具是什么，以及使用OpenAI的o1模型作为我们的集成器。我们惊讶地发现一些技术_没有_帮助，比如Sonnet 3.7的思考模式和在实现代理后运行单独的"修复回归"代理。虽然我们探索了一些基本的集成技术，如多数投票（例如，比产生Anthropic 70.3%结果的系统更基本），但我们决定不进一步研究这个方向，因为它引入了显著的额外成本，在当前模型服务成本下不适合实际使用。

接下来，我们正在通过强化学习和专有数据微调我们自己的模型，以显著改善用户体验，通过更快、更便宜的代理，同时在SWE-bench验证榜单上保持类似的分数。

## SWE-bench实际测试的是什么？

[SWE-bench](https://www.swebench.com/)测试AI系统如何处理从流行开源项目的实际GitHub问题中提取的软件工程任务。一些示例问题可以在OpenAI的[关于该基准测试的原始博客文章](https://openai.com/index/introducing-swe-bench-verified/)中找到。大多数编码基准测试专注于孤立的Leetcode风格编程问题，而SWE-bench涉及代码库导航、针对一套回归测试进行迭代，以及整体上更多的复杂性。

对于SWE-bench中的每个问题，AI代理都会获得一个代码库（预先安装依赖项）和任务描述。它不会被告知必须运行哪些测试来验证解决方案是否有效，而是必须通过查找相关的回归测试并编写自己的复现脚本来解决这个问题。它需要自己弄清楚如何运行测试，这意味着它需要像人类程序员一样"入职"每个代码库。然后，它必须完全自主地导航以将解决方案应用到代码库。这涉及编辑文件、使用bash工具运行测试、运行"grep"和"ls"等bash命令、创建复现脚本以及反思其解决方案。

然后，基准测试会检查代理提交的最终解决方案是否通过了一套保留的新测试（检查新功能）和回归测试（检查现有功能）。

## SWE-bench作为基准测试的优缺点

虽然SWE-bench对AI研究社区来说是一个令人难以置信的资产，但没有一个基准测试是完美的。SWE-bench严重倾向于修复小错误而不是创建新功能，_任务描述比我们在现实生活中发现开发人员向代理提供的提示更加详细和对LLM友好。_ 它还只包括Python项目，缺少实际开发环境中的语言多样性。考虑到失败测试的错误消息在Python中往往比在Java和C++等语言中更具描述性，这使得Python成为代理更容易使用的语言。此外，考虑到生产代码库通常比开源代码库大几个数量级，需要更复杂的代码库感知和导航能力。最后，OpenAI发现SWE-bench验证中只有[8.4%的问题](https://openai.com/index/introducing-swe-bench-verified/)需要有经验的软件工程师花费超过一小时来解决。

现实世界的软件工程涉及协作、迭代和上下文，这些是现有基准测试无法完全捕捉的。Augment的生产编码代理受益于与Linear、Jira、Notion、Google搜索、Slack等第三方软件的集成。我们的生产代理还能在遇到困难时向开发人员提问。最后，它们会记住开发人员提供的反馈和提示，因此它们的性能会随着时间的推移而提高。SWE-bench（以其当前形式）无法测试这些功能。

在Augment，我们非常关注为客户构建最高质量的产品体验，这包括底层最好的AI技术。这就是为什么我们一直在思考如何改进基准测试的状态。例如，我们最近与世界分享了关于[AugmentQA](https://www.augmentcode.com/blog/you-make-your-evals-then-your-evals-make-you-introducing-augmentqa)的一些细节，这是一个旨在通过直接从真实世界软件开发场景中获取的现实问答任务来衡量存储库感知代码检索的基准测试。

## 我们学到了什么

我们发现SWE-bench验证的分数主要由基础模型的质量驱动。优化提示很重要，但作为改进的轴心很快就会饱和。除此之外，集成技术可以带来3-8%的增益。集成有帮助是有道理的，因为代理的结果高度不稳定。我们发现，在50个示例中对我们的任何两个运行进行采样，两次运行之间的结果会有所不同。

我们也对代理如何利用"grep"和"find"等工具在SWE-bench中导航代码库印象深刻。然而，虽然这种代码库导航技术对SWE-bench效果很好，但我们发现对于实际用例，由于其处理模糊用户输入和大型代码库的能力，这种方法目前存在局限性。我们发现了无数类似的例子，改进我们生产编码代理质量的变化（通过定性客户反馈衡量）并没有在SWE-bench上产生影响。

鉴于这些经验，我们认为作为应用层AI编码公司进行研究的正确方式是专注于通过强化学习微调开源模型，从而显著改善成本和延迟。通过训练速度显著更快且便宜到足以在更大规模集群中运行的代理，可以实现全新类别的AI编码体验。关于这一点，我们将有更多内容。

同时，我们认识到代理的定量评估与之前几波AI技术的评估一样存在深刻的不完善之处。有一长串需要改进的地方，以提供更优质的产品，而这些在SWE-bench等评估中并未体现。我们继续专注于跟踪和优化所有这些问题。

## 我们是如何做到的：深入探讨

简而言之，我们尝试了所有顶级模型、工具和测试时计算技术，最终收敛到一个结合Anthropic的Claude Sonnet 3.7模型和OpenAI的O1模型最佳方面的系统。

在此过程中，我们进一步深化了构建代理的专业知识。这些见解在我们构建和完善代理功能（包括IDE代理和其他即将推出的功能）时证明是无价的。对于这第一次提交，我们决定不训练自己的模型，但正在研究这作为可能的下一个项目，以改善运行代理的成本和延迟。

为了补充Anthropic自己的SWE-bench博客文章中未公开的"规划"工具，我们发现使用他们的["顺序思考"MCP](https://github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking)是有效的。这很重要，因为Sonnet 3.7的思考模式在SWE-bench的上下文中并不有效。我们尝试了基于字符串替换的文件编辑工具的变体，如智能粘贴模型，但发现这不是提高分数的有希望方向。同样，我们还尝试了bash工具的变体，但发现这些也不影响最终分数。我们探索了添加各种基于嵌入的检索工具，但发现对于SWE-bench任务，这不是瓶颈——"grep"和"find"就足够了。（在实践中，我们发现基于嵌入的工具对于提供出色的产品体验至关重要。）

我们探索了一些将代理工作流分解为具有单独提示的单独代理运行的技术。这包括添加一个初始定向代理运行，用于弄清楚如何运行测试，以及添加一个最终的"修复回归"代理运行，用于修复候选解决方案引入的现有测试中的任何回归。我们发现这个研究方向最终并不是很有成效。虽然"修复回归"代理能够找到并修复一些回归，但它也在原本正确的候选解决方案中引入了错误，导致最终分数没有净改善。

对于集成，我们使用了一个简单的多数投票技术，结合OpenAI的o1模型，向它展示候选差异列表以及问题陈述，并要求它选择多数投票解决方案。我们发现除了候选差异之外提供额外上下文并没有帮助。我们发现o1在集成方面比Sonnet 3.7好几个百分点。我们没有进一步研究集成路线，因为在实际环境中使用成本太高。

以下是我们开始每次代理运行的指令，从Anthropic的[指令](https://www.anthropic.com/engineering/swe-bench-sonnet)分叉而来：

```
<uploaded_files>
{location}
</uploaded_files>
我已经在目录{location}（不在/tmp/inputs中）上传了一个Python代码库。考虑以下PR描述：


<pr_description>
{pr_description}
</pr_description>


你能帮我对代码库进行必要的更改，以满足<pr_description>中指定的要求吗？
我已经处理了<pr_description>中描述的所有测试文件的所有更改。这意味着你不需要以任何方式修改测试逻辑或任何测试！


你的任务是对{location}目录中的非测试文件进行最小更改，以确保满足<pr_description>。


按照以下步骤解决问题：
1. 首先，最好探索一下代码库，熟悉其结构。
2. 创建一个脚本来复现错误，并使用BashTool执行`python <filename.py>`，以确认错误。
3. 使用sequential_thinking工具规划你的修复。思考5-7个不同的可能问题来源，并将其提炼为1-2个最可能的来源。
4. 编辑代码库的源代码以解决问题。
5. 重新运行你的复现脚本，确认错误已修复！
6. 考虑边缘情况，确保你的修复也能处理它们
7. 从代码库运行选定的测试，确保你的修复不会破坏其他内容。


如何使用"sequential_thinking"工具的指南：
- 你的思考应该是彻底的，所以很长也没关系。将totalThoughts设置为至少5，但设置到25也可以。当你考虑多个可能的解决方案或问题的根本原因时，你需要更多的总体思考。
- 根据需要使用此工具，以提高答案的质量。
- 你可以在思考之间运行bash命令（如测试、复现脚本或'grep'/'find'以查找相关上下文）。
- sequential_thinking工具可以帮助你分解复杂问题，逐步分析问题，并确保问题解决的彻底方法。
- 在整个思考过程中，不要犹豫多次使用它，以增强解决方案的深度和准确性。


提示：
- 你必须在{location}目录中进行更改，以确保满足<pr_description>中指定的要求。保持目录不变不是有效的解决方案。
- 不要在传递给sequential_thinking工具的思考中进行工具调用。例如，不要这样做：{{'thought': '我需要查看这个版本的Django中`apps.get_models()`的实际实现，看看是否有bug。让我检查Django apps模块：\n\n<function_calls>\n<invoke name="str_replace_editor">\n<parameter name="command">view</parameter>\n<parameter name="path">django/apps/registry.py</parameter></invoke>', 'path': 'django/apps/registry.py'}}
- 尊重工具规范。如果某个字段是必需的，请确保为其提供值。例如，sequential_thinking工具需要"thoughtNumber"。
- 当你运行"ls"或其变体时，你可能会看到一个符号链接，如"fileA -> /data/vol/fileA"。你可以安全地忽略符号链接，在读取、编辑或执行文件时只使用"fileA"作为路径。
- 当你需要查找有关代码库的信息时，使用bash工具中的"grep"和"find"搜索相关文件和代码
- 使用bash工具设置任何必要的环境变量，如运行测试所需的变量。
```

有关我们如何在SWE-bench上获得榜单第一名的更多详细信息，您可以在GitHub上访问我们的开源实现[点击这里](https://github.com/augmentcode/augment-swebench-agent)。祝编码愉快！

---

**关于作者：**  
**Colin Flaherty** 是Augment的创始研究员，致力于AI代理和检索系统研究。在此之前，他是Facebook AI Research的研究员，是"Cicero"的共同作者——一个掌握了外交游戏的AI，被广泛认为是继国际象棋、围棋和扑克之后AI游戏的下一个重大挑战。

**Tongfei Chen** 是Augment Code的研究科学家，专注于代码生成和信息检索。此前，他在微软担任高级研究员四年。他获得了约翰霍普金斯大学计算机科学博士学位。
