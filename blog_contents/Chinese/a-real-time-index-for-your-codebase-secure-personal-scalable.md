# 代码库的实时索引：安全、个人化、可扩展

**作者：** Markus Rabe, <PERSON> Mei<PERSON>  
**日期：** 2025年1月29日

_摘要：在Augment，我们构建了一个安全、个性化的代码索引系统，它：_

* _在代码更改后几秒钟内更新（而竞争对手需要10分钟延迟）_
* _使用自定义AI模型而非通用嵌入_
* _使用Google Cloud每秒处理数千个文件_
* _为每个开发者维护单独的索引以处理分支切换_
* _通过所有权证明和无第三方API实现严格安全_
* _通过在用户之间共享重叠索引高效管理RAM使用_

_结果：上下文感知AI能够真正跟上实际开发工作流程，同时保护代码安全。_

## 检索的不同方法

检索领域的主流方法仍然是调用通用模型API（如OpenAI）为代码文件和文档片段创建嵌入，然后使用嵌入搜索API（如Pinecone）检索与查询相关的片段。然而，这种方法导致质量差、延迟高，甚至可能存在安全隐患。

因此，Augment采取了不同的方法：我们开发了一个定制的索引和嵌入搜索系统，旨在为您提供所有产品中最相关和最新的上下文。

## 为每个开发者提供个人索引

我们所有的产品都能理解您的代码库。但什么才是真正"您的代码库"？在现实世界中，这是一个出人意料的棘手问题。

![应用截图](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67e6efa8ed68f8096e3e8d94_ss-app.webp)

考虑这样一个场景：您刚刚收到一条关于您的拉取请求的评论，要求您重命名一个函数。您想快速解决这个问题并继续前进。因此，您切换到该分支并浏览该函数名称的所有定义和用法，并可能对它们进行调整。在更新PR之前，您可能还会合并或变基您的PR，使其与您要合并到的分支保持同步，并解决任何合并冲突。

从主分支或开发分支检索信息是不够的：相关函数可能在其他分支上根本不存在，如果模型面对上下文中未定义的名称，很可能会产生幻觉。根据我们的经验，我们的一些竞争对手每10分钟更新一次上下文，这也是不够的，因为专业开发人员往往相当频繁地切换分支。从错误的分支检索可能甚至会重新引入最近被团队成员消除的问题和模式，加剧在大型团队中工作的痛苦。

_不尊重您正在处理的代码确切版本的AI很容易让您和您的团队花费比节省更多的时间。_

因此，我们为每个用户维护一个代码库的实时索引。

## 跟上您的代码库

在git中更改分支、搜索替换和自动格式化可以在一秒内快速更改数百个文件。就像上面的例子一样，这类指令很常见，尤其是对于大型组织，数百或数千名开发人员将更改合并到同一代码库中。

我们的目标是在文件发生任何更改后的几秒钟内更新您的个人搜索索引，以便下一个预测能够受益于完整的上下文感知。这对索引系统提出了严格的要求。

我们的索引系统架构大量使用Google Cloud，特别是PubSub、BigTable和AI Hypercomputer。这些技术提供了高度可靠和可扩展的基础，使我们的服务轻量化和简单化。为了充分利用我们的GPU，嵌入模型工作器基于我们的[自定义推理堆栈](https://www.augmentcode.com/blog/rethinking-llm-inference-why-developer-ai-needs-a-different-approach)构建。

![架构图](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67e6efebcdcc205890938f70_chart.webp)

如今，我们的索引系统能够每秒处理数千个文件，这意味着您的分支切换几乎是即时处理的。我们的架构还允许我们扩展到远超这些数字的规模，唯一的限制是成本，主要是我们的GPU创建嵌入的成本。

## 平衡工作负载

除了来自用户日常活动的吞吐量峰值外，我们的索引系统还需要处理批量工作负载。批量上传的第一种情况是当新用户注册或现有用户检出新代码库时，我们可以观察到10万个或更多文件的上传。在这些情况下，我们的目标是不让客户等待超过几分钟就能开始使用，因此如果有可用的吞吐量，我们希望将其全部用于批量上传。

批量上传的另一种情况是部署新的搜索索引。我们的上下文感知能力在不断提高，这使得这成为一个出人意料的常见事件。这意味着新的嵌入模型需要在影子模式下运行可能延长的时间以赶上进度。当一个客户可能已经切换到新的搜索索引时，其他客户可能仍处于追赶模式。

我们确保所有用户都能享受到即时吞吐量，使他们的个人搜索索引与代码库保持同步，即使在多个批量作业重叠的时期也是如此。我们通过在PubSub中维护单独的队列来实现这一点，保持其他队列的长度刚好足以使嵌入模型工作器中的GPU饱和。

## 您的代码始终是您的代码

[Augment优先考虑您的代码安全](https://www.augmentcode.com/security)，采用安全设计方法，遵循数据最小化、最小权限和故障安全机制等原则。我们在Google Cloud上自托管嵌入搜索，避免使用可能暴露嵌入的第三方API，研究表明这些嵌入可以被逆向工程为源代码（[arXiv 2305.03010](https://arxiv.org/abs/2305.03010)，[arXiv 2004.00053](https://arxiv.org/abs/2004.00053)）。

管理具有不同访问权限的多个代码库（由于收购、商业机密或承包商协作）可能会导致未授权的数据暴露风险。Augment通过所有权证明解决了这个问题：IDE必须通过向我们的后端发送文件内容的加密哈希值来证明它知道文件内容，然后才允许从文件中检索内容。这确保预测严格限制在用户有权访问的数据范围内，防止受限信息泄露。

## 提供嵌入搜索服务

我们在每次按键时对您的代码库执行多次嵌入搜索。但提供低延迟的个性化搜索索引可能成本高昂。对于大型代码库，所有片段的嵌入大小可以轻松达到10 GB，为了保持低延迟，我们需要将嵌入的相当一部分保留在RAM中。但长时间为每个用户使用那么多RAM会使我们的服务成本膨胀。

为了解决这个问题，我们在来自同一租户的用户之间共享重叠的搜索索引部分。为了保证上述所有权证明原则，我们使用了自己的嵌入搜索实现。我们的嵌入搜索服务不仅对嵌入进行评分以找出最相关的内容，还验证客户端已证明它有权访问内容。

## 自定义模型

通用嵌入模型擅长识别哪些文本片段是_相似的_。然而，通用模型的嵌入很容易错过在文本层面上不相似的大量相关上下文。调用站点不一定与函数定义相似，文档不一定与它引用的代码相似，不同语言的代码即使实现相关功能也不一定相似。

除此之外，即使是高度"相关"的文档也可能不需要检索。例如，我们的代码补全LLM非常熟悉流行的开源库，如PyTorch。向该LLM展示PyTorch实现的"相关"部分并不会提高其输出质量，而有限的提示空间可以用于其他内容。因此，我们需要_优先考虑有用性而非相关性_。

根据我们的经验，随着代码库规模的增大，通用嵌入模型的价值迅速下降，因为大多数嵌入模型会被杂乱内容混淆。虽然单个工程师开发的项目可能使用通用嵌入模型效果不错，但我们发现它们对于大型团队的代码库来说是不够的。

为了帮助使用大型复杂代码库的专业软件工程师，我们开发了一套自定义上下文模型，这些模型经过专门训练，能够识别最有帮助的上下文。

## 亲自尝试！

Augment的上下文感知源自自定义模型和个性化上下文，并且具有强大的安全性。无论您是处理大型代码库还是在处理您的副项目，Augment确保您随时掌握正确的上下文。[注册Augment亲自体验。](https://login.augmentcode.com/u/login/identifier?state=hKFo2SBjLU12eThvUVFWMVU3TGdSTDdVM0J5WUZYVk9fZWZBMqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDd2ZExFYkk0X0pDQU9xMDN5U1RHdmNDMUFEanc0dTYto2NpZNkgaVJ3Nk91dU0zOHJHZnhtem5qaGNCbVdVSXhYRFIxT0g)

---

**关于作者：**  
**Markus Rabe** 是一位工程师和研究员，在机器学习、自动推理和形式化方法领域发表过多篇论文。在加入Augment之前，他在Google Research与Christian Szegedy共同担任技术负责人，负责[Memorizing Transformers](https://arxiv.org/abs/2203.08913)和[Flash Attention](https://arxiv.org/abs/2112.05682)的前身等工作。

**Dirk Meister** 是一位软件工程师，拥有存储系统和数据库背景。在加入Augment之前，他是Databricks的高级软件工程师，负责内部开发者工具和测试基础设施。Dirk拥有德国美因茨古腾堡大学计算机科学博士学位。
