# 编码AI如何支持大规模软件工程

**作者：** <PERSON>  
**日期：** 2025年1月7日

_摘要：_ _大型、长期存在的软件项目对人类事业至关重要，但极其难以创建和发展。如今的编码AI远未能解决软件工程的真正痛点。Augment Code正在赋能团队克服这些挑战——从灵感到软件卓越，轻松快速地实现。_ 

程序员和雇佣他们的公司正在思考AI将如何影响软件工程。AI会[让编程重新变得快乐](https://www.augmentcode.com/blog/reimagining-software-engineering-through-ai)还是[摧毁软件工作](https://nypost.com/2024/08/21/business/amazon-software-engineers-could-stop-coding-soon-due-to-ai/)？我们是处于[泡沫中](https://www.forbes.com/sites/bernardmarr/2024/08/07/is-the-ai-bubble-about-to-burst/)，[推动人类进步](https://www.nobelprize.org/all-nobel-prizes-2024/)，还是两者兼而有之？

**软件工程很难。** 我的职业生涯一直与非常有才华的团队一起维护大型复杂代码库，无论你对AI潜在影响有什么看法，我们都可以同意，大规模开发软件异常困难。多年来我们取得了实质性进步——更高级的语言、集成开发环境(IDE)、持续集成和交付(CI/CD)、开源和公共云。然而，软件通常仍然达不到我们的期望：难以使用、脆弱、不安全且缺乏功能。我从未遇到过一个没有长长的功能请求清单和重构改进愿望的项目。

此外，随着软件项目的扩大和成熟，增强它们变得更加具有挑战性。如今的代码库可能非常庞大——例如，开源Chromium拥有超过3000万行代码(LOC)，[Uber维护超过9000万LOC](https://www.uber.com/blog/nilaway-practical-nil-panic-detection-for-go/)，而Google有其他包含[超过20亿LOC](https://www.youtube.com/watch?v=W71BTkUbdqE)的代码库——这使得它们极难导航和理解，更不用说修改了。随着时间推移，软件往往会积累技术债务，使其更加脆弱。现代程序通常依赖于相互关联的数据存储、库和服务的复杂网络，而这些组件本身也在不断变化。程序现在很可能是分布式的，导致更多潜在的故障点，而软件需要从这些故障中恢复，同时也加剧了性能优化和安全性问题。大型项目需要大型团队，通常跨不同时区工作，随着新开发人员加入和其他人离开，参与者也在不断变化。

因此，如今的软件开发和维护成本更高，而且[质量可能实际上正在下降](https://stackoverflow.blog/2023/12/25/is-software-getting-worse/)。仅美国经济每年因[软件故障造成的损失估计为2.4万亿美元](https://www.it-cisq.org/the-cost-of-poor-quality-software-in-the-us-a-2022-report/)。损坏的软件无法[吞噬世界](https://a16z.com/why-software-is-eating-the-world/)**。**

## AI是软件工程生产力的巨大飞跃

我们的论点是，辅助开发人员的编码AI已经比之前的飞跃更大程度地提高了生产力：由于其自然语言能力，AI可以在程序员选择的任何抽象级别上工作。

一项研究确定，GitHub Copilot使开发人员[完成任务的速度比没有AI时快55%](https://visualstudiomagazine.com/articles/2024/09/17/another-report-weighs-in-on-github-copilot-dev-productivity.aspx#:~:text=The%20group%20that%20used%20GitHub,didn't%20use%20GitHub%20Copilot.)。现在，一家从Copilot转向Augment Code的财富500强客户在已经通过Copilot取得的成就基础上又实现了40%的额外改进。这种生产力提升反映了一个仅理解基本编程语言和算法的AI（想想新毕业的计算机科学学生）与像Augment这样深入理解你的代码库的AI（想想你团队中的专家）之间的差异。

## 提供软件工程AI的7个基本信念

在Augment，我们正在构建一个AI平台，以_规模化_方式改进软件质量和软件工程实践——我们正在创新，帮助你成功改进最大型、最复杂代码的代码库！

以下是指导我们如何为专业软件团队解决这个问题的几个核心信念：

**上下文就是一切。** 程序中蕴含的知识极其宝贵：用户界面、API、数据模型、架构、安全策略、性能优化、故障恢复等，所有这些通常都是经过多年逐步改进的。但如今的编码AI普遍缺乏对你的代码库以及你如何构建软件的专业知识。相反，Augment努力将你的代码、文档和支持生态系统中体现的_所有_知识用于帮助你改进软件。始终拥有丰富的上下文使Augment的AI感觉更加自然，避免了设计提示或纠正幻觉的需要。

**熟悉陌生代码的过程是一个隐藏的、未解决的问题：** 软件开发中最大的隐藏成本是工程师花在[试图理解不是他们编写的代码](https://nicholas.carlini.com/writing/2024/how-i-use-ai.html)上的时间：凌晨2点被不是你编写的代码引起的严重事故惊醒，疯狂地试图理解它。或者，作为基础设施和安全工程师，审查其他团队工程师在代码库各个部分提交的数百个PR。因为Augment深入理解你的软件，我们大大加速了你的生产力提升，在需要的时候和地方提供见解，同时[减轻甚至消除](https://www.augmentcode.com/blog/pigment-automates-boilerplate-tasks-and-simplifies-testing-with-augment-code)高级工程师指导新团队成员的负担。

**提高质量比增加代码更重要。** 简单的编码AI非常乐意向你的代码库添加冗余代码。鉴于其深度上下文感知能力，Augment更能够建议重用代码甚至_删除代码_。同样，Augment更好地理解你的应用程序架构、数据模型、最佳实践、编码约定等，使我们能够提供更高质量的建议，甚至帮助你将子系统移植到C++或Rust以提高性能。此外，Augment可以识别趋势，帮助加速代码库的预期演变，例如过渡到改进的算法、数据库或第三方库。

**在大型系统中，没有所谓的简单更改——但AI可以为你处理这种复杂性。** 如果编程是构建一个玩具飞机，那么软件工程就是构建一架空中客车A380并在飞行中不断改造它。每一个变化都会以人类难以完全理解的方式在复杂系统中产生连锁反应。Augment的Next Edits是最早能够在整个复杂代码库中映射提议的代码修改影响的代理之一。虽然今天我们正在帮助工作流程，以及加速测试和迁移，但明天的获胜AI将承担更多这种复杂性负担。

**软件开发发生在IDE之外。** 应用程序开发很少是个人运动，然而今天的编码AI往往只关注个人生产力。相反，Augment努力在团队工作的地方与他们会面。考虑Augment内部开发中的这个最近交流：一位工程师在Slack上提出关于错误消息的问题。另一位将Augment添加到该线程以获取我们AI的看法。Augment利用其深度代码库感知和其他上下文，在Slack中回应这看起来像一个bug，原始工程师分配了一个PR来进行Augment建议的修复。

**_增强_而非替代开发者。** 人类擅长长期演进软件系统所需的复杂推理：我们应该将这个程序迁移到云端吗？我们应该切换到微服务架构吗？缓存会在多大程度上改善用户体验并减轻后端数据库的负载？像这样的软件工程决策仍然超出了今天的AI能力范围。同时，工程师在管理与复杂重构或升级相关的细节，或者处理业务关键软件必不可少的重复任务（有人需要单元测试吗？）方面做得不那么好。

因此，人类洞察力将继续塑造软件生命周期，而机器智能则减少困扰工程团队的辛劳，使他们能够更多地专注于创造性问题解决、产品质量和客户满意度。超级智能（Dario Amodei在一篇精彩文章中称之为["强大的AI"](https://darioamodei.com/machines-of-loving-grace)）有朝一日可能会打破这种平衡，但我们Augment相信这仍然需要多年时间。同时，像Augment这样的平台大大加速了从新手到专家软件工程师的旅程，同时使这个过程不那么痛苦。

**AI实际上会增加对软件工程师的需求。** 如果你能够交付_所有_积压的功能，同时重构以消除技术债务，会怎样？今天，公司由于回报不可预测而人为地限制其软件投资——功能缓慢到达，质量受损，预算超支，安全性仍然是一个持续关注的问题。但当AI显著提高软件开发的生产力和可靠性时，经济基本面发生变化。组织将扩大其软件计划，因为他们最终将获得一致、可靠的投资回报。这种增加的可预测性和产出不会减少对工程师的需求——相反，它将释放以前未开发的商机，需要更多的工程人才来实现这些软件梦想。实际上，乐观的观点是，随着软件的显著改进，将释放实质性的经济增长，实际上增加对工程人才的需求。

**看看Augment能为你和你的团队做什么。** _AI正在提升软件工程学科，而Augment Code的使命是驯服构建和发展运行世界的软件的复杂性。_ 我们希望与你一起构建更美好的未来。[请加入我们！](https://www.augmentcode.com/waitlist)

---

**关于作者：**  
在加入Augment Code之前，"Dietz"曾担任Pure Storage的CEO。在他的领导下，Pure从0增长到超过10亿美元的收入，从15名员工增长到数千名员工，并成功上市。作为4次创业者，他获得了卡内基梅隆大学计算机科学博士学位，专注于机器学习。
