# 个人旅程：通过AI重新想象软件工程

**作者：** Scott Dietzen  
**日期：** 2024年8月15日

很久以前，我有幸被卡内基梅隆大学录取，并被教师的素质和热情吸引进入计算机科学领域。2018年图灵奖获得者Geoff Hinton教授了我的AI入门课程，我亲眼目睹了符号推理支持者和神经网络支持者之间的辩论（当时神经网络的支持者实际上只有Geoff一人）。我最终在CMU度过了十年时光收集学位，最终撰写了一篇关于编程语言理论方法应用于机器学习的论文，尽管是在传统的符号推理AI领域。

在大学期间，我是一个高产的程序员，有时甚至会在周末晚上带上几瓶啤酒去计算机实验室。自从最终毕业后，我一直是一名创业者——Augment是第五家接纳我的初创公司。每次创业的成功都依赖于杰出工程师打造的一流软件。人们可能会认为，极具天赋的团队取得重大成功应该是非常有趣的。虽然在这些旅程中确实有许多令人满意的时刻，但我仍然惊讶于我曾经体验到的独自编程的乐趣为何无法转化到更有才华的团队构建更有意义的软件中。为什么？与优秀的人一起做伟大的工作不是应该更有趣吗？

相反，软件团队继续被以下问题所困扰，例如：

* 太多繁琐工作，整理平凡的代码和测试；
* 花太多时间尝试理解代码；
* 更新库和依赖项的工作太多；
* 代码重用和重构需要太多努力；
* 花太多时间进行代码审查和培训新人；以及
* 太多会议尝试协调工作。

最重要的是，工程师受到他们所处理的代码质量参差不齐的阻碍。尽管人类每年在软件工程师薪资上投入超过1万亿美元，但大多数软件仍然令人失望——太复杂、太脆弱、太慢、不安全、不完整等。软件要么维护成本非常高，要么在投资减少后变成僵化的遗留系统。如果软件质量不足，它就无法吞噬世界——人类进步在很大程度上受限于我们突破这些软件质量和软件工程生产力的瓶颈。

大型语言模型（LLM）应运而生。像许多人一样，我对Transformer推动的创新和生成式AI的力量感到惊讶——我们可以将简单的下一个标记预测扩展到如此多样化的机器学习（ML）应用。对我来说，显而易见的下一个问题是：我们能否利用ML来大规模改进软件和编程？

在我开始思考这些问题的时候，我正处于"休假状态"，在Pure Storage度过了一段精彩的时光，帮助公司从15名员工扩展到近2000名员工，从0美元增长到10亿美元的收入，成功上市，然后招募了一位拥有更多规模经验的人来接管。感受到志同道合的精神，早期的Augurs邀请我加入他们的研究会议，这些会议最终促成了Augment, Inc.和我们将在未来几个月推出的产品（请继续关注或加入我们的早期访问计划）。我在2023年初加入团队时更加兴奋。（我想我可能比在之前的公司更降低了Augment的平均智商😉）。

此后发生的一切都重申了这一信念的飞跃。虽然GitHub CoPilot和ChatGPT等当前工具确实帮助了个别程序员，但它们并未解决大规模编程的复杂性：虽然它们在编程方面具有一定的熟练度，但它们无法利用现有代码库中表示的知识——它们的功能更像是一个刚毕业的计算机科学学生，而不是一个沉浸在你环境中的程序员；虽然它们确实关注个别程序员，但它们不知道程序员是更广泛团队的一部分；虽然它们使添加代码变得容易，但它们没有足够地帮助重用代码，因此你的软件变得更臃肿、更复杂、更脆弱。我梦想有一个能删除代码的AI！

未来十年将见证自高级语言出现以来软件质量和团队生产力的最大飞跃，以及能够进行更深层次推理的AI，恢复软件创作的乐趣。我们希望你能与Augment一起踏上这段旅程。

---

**关于作者：**  
在加入Augment Code之前，"Dietz"曾担任Pure Storage的CEO。在他的领导下，Pure从0增长到超过10亿美元的收入，从15名员工增长到数千名员工，并成功上市。作为4次创业者，他获得了卡内基梅隆大学计算机科学博士学位，专注于机器学习。
