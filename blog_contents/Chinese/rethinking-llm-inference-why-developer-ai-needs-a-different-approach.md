# 重新思考LLM推理：为什么开发者AI需要不同的方法

**作者：** Markus Rabe, Carl Case  
**日期：** 2024年11月14日

_摘要：我们相信完整的代码库上下文对开发者AI至关重要。但处理所有这些上下文通常会以延迟为代价。在Augment，我们正面迎接这一挑战，推动LLM推理可能性的边界。这篇文章分解了编码推理的挑战，解释了Augment优化LLM推理的方法，以及构建我们的推理堆栈如何为客户提供卓越的质量和速度。_

## 为什么上下文很重要

对于编码，上下文就是一切。代码库中的更改不仅取决于当前文件，还取决于依赖项和调用站点、README文件、构建文件、第三方库等等。大型语言模型在使用额外上下文方面表现出令人难以置信的灵活性。在Augment，我们一次又一次地了解到，提供更多相关上下文可以提高我们产品的质量。

[这个例子](https://youtu.be/k5IxQ_1cYZg)展示了上下文中的小提示对编码有多重要。Augment已经掌握了为大型代码库中的每个补全和每个聊天请求检索最相关信息片段的艺术。

## 优化解码还是上下文处理？

我们的内部研究表明，当上下文长度远超过10,000个标记时，代码预测的质量会持续提高。然而，在提示中添加如此多的上下文意味着我们需要处理大量标记，这对低延迟响应提出了挑战。

请注意，AI代码中的上下文标记和输出标记的平衡与标准AI聊天应用程序非常不同：典型的聊天问题有大约100个输入标记和数百个输出标记。但我们通常有数千个上下文标记，而且通常只有几十个输出标记。

开源社区今天提供了功能强大的推理解决方案，如[vLLM](https://github.com/vllm-project/vllm)和[TensorRT-LLM](https://github.com/NVIDIA/TensorRT-LLM)。但现有的解决方案和基准测试针对的是具有短提示（很少上下文）和长回答的聊天用例。专有服务似乎也是如此：friendli.ai的一篇[优秀博客文章](https://friendli.ai/blog/comparative-analysis-ai-api-provider)表明，这些服务提供的首个标记时间(TTFT)对于超过1k标记的输入会急剧增加。他们报告，Llama3 70B对于10k标记提示的最佳TTFT约为1000毫秒。

在开发我们的推理堆栈时，我们面临多个决策点，需要在上下文处理和解码速度之间进行优化平衡。由于我们产品的用户体验主要取决于上下文，我们执着地优先考虑上下文处理速度。结果是一个能够以不到300毫秒的TTFT（快3倍）为Llama3 70B提供10k输入标记请求的推理堆栈，而且这还不包括我们独特的缓存方法。

另一个参考点是fireworks.ai的一篇[博客文章](https://fireworks.ai/blog/fireattention-v3#llama-8b)，它比较了他们的服务与vLLM和TensorRT-LLM对于Llama 8B模型，使用30k上下文标记和256输出标记，使用8个GPU（他们的服务使用AMD MI300 GPU，TensorRT-LLM和vLLM使用NVIDIA H100）。他们显示这些选项保持在每秒5个请求以下。相比之下，我们的推理系统可以在8个H100上提供近10个请求/秒。所有数字都是在没有推测性解码和缓存的情况下获得的。

![性能比较图](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67e6f4ca98f8bda239814f2e_download-220(1).webp)

## 关于FLOPS和内存带宽

高性能GPU，如NVIDIA的H100系列，价格昂贵，但它们每秒可以执行令人难以置信数量的数学运算，以FLOPS（每秒浮点运算）衡量。然而，如果你比较处理单个请求所需的计算量与设备的总FLOPS和回答请求所需的时间，你最终会得到低于1%的FLOPS利用率。大部分计算能力被浪费了。

主要原因是解码在FLOPS利用率方面效率低下。考虑前向传递的运行时间作为批处理大小的函数。

![运行时间与批处理大小图表](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67e6f50eda498227a52d56f0_chart-2.webp)

作为第一近似，可达到的运行时间主要受GPU完成所有计算所需时间（FLOPS限制）和GPU将模型权重从GPU内存加载到缓存所需时间（内存带宽限制）的影响。计算需求随批处理大小线性增长，但模型权重只需加载一次，因此带宽限制不会受到显著影响。这两条曲线的交叉点是我们从内存带宽限制区域过渡到FLOPS限制区域的地方。对于高性能GPU，这个交叉点非常高，我们需要几百甚至几千个标记在批处理中才能接近FLOPS限制。

上下文处理自然受FLOPS限制，但解码对我们的FLOPS利用率可能有一个不直观的巨大影响：假设交叉点在512，我们为单个标记执行解码步骤。这一步骤所需的时间大致相当于处理512个标记所需的时间。所以我们只使用了GPU可以提供的FLOPS的约1/512≈0.2%。在简单实现中，即使每个请求的解码标记数量很少，也会严重降低平均利用率。

## 标记级批处理

传统的批处理策略将多个请求的解码步骤分组到同一批次中。请注意，这并不从根本上改变低利用率问题：考虑将10个正在解码的请求批处理在一起。如果我们的交叉点是512个标记，从内存带宽限制转变为FLOPS限制，那么我们仍然只利用了可用FLOPS的10/512或约2%。

我们的批处理策略直接解决了这个问题。为了避免解码步骤中的低FLOPS利用率，并尽可能保持在FLOPS限制区域，我们允许解码步骤"搭便车"在其他请求的上下文处理上。

![标记级批处理图](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67e6f53fa01635f85999f065__chart-3.webp)

我们构建的批次混合和匹配来自多个请求的标记。要构建下一个批次，我们询问推理队列中的所有请求它们想要处理哪些标记。如果请求处于解码阶段，它将贡献一个标记，如果请求处于上下文处理阶段，它可能会贡献很多标记。

虽然非常大的批处理大小比大批处理大小具有略高的FLOPS利用率，但总体请求延迟还需要考虑解码速度。核心问题是Transformer每批只能生成一个输出标记（忽略推测性解码），因此总体请求延迟随单个批次的运行时间线性增长。为了最大化吞吐量并最小化延迟，我们因此选择接近交叉点的批处理大小，在这里我们接近最小运行时间并接近最佳FLOPS利用率。

学术文献最近也采用了这种方法，我们想强调关于Sarathi的优秀论文（[链接1](https://arxiv.org/pdf/2308.16369)，[链接2](https://arxiv.org/pdf/2403.02310)）和DeepSpeed-FastGen（[链接](https://arxiv.org/pdf/2401.08671v1)）。学术文献称这种技术为"分块预填充"。

## 生产推理系统的要求

### 取消

除了纯粹的速度和可预测性外，生产环境中的推理系统还有许多不明显的要求。其中之一是请求取消：我们在每次按键时都会触发推理请求。这保证了最佳体验，但可能会快速压垮后端，因为连续的按键可能会以近50毫秒的间隔到达，超过推理服务器的速度。在简单实现中，这意味着用户可能有多个请求在处理中，而我们知道实际上只需要最新的请求。

为了避免这个问题，重要的是可靠地取消被识别为不必要的工作。我们的批处理策略使这成为可能，因为每个批次相对较小，我们可以在任何批次之后停止请求的计算。它也与缓存配合得很好，因为如果下一个请求的前缀与上一个请求匹配，我们可以重用上一个请求的部分完成的上下文处理。

### 部署规模

有替代的批处理策略，将上下文处理和解码分成单独的GPU组，并在它们之间发送预填充的KV缓存。但这增加了可以独立部署的最小单元的大小，当扩展到多个数据中心和不同工作负载时，会增加痛苦的复杂性。相比之下，我们的批处理策略允许我们将上下文和输出标记的处理放在一起，从而保持部署紧凑和统一。

## 我们的优化过程

任何优化工作中最重要的步骤是在开始时选择_要优化什么_。我们专注于单一硬件平台（NVIDIA H100）和单一模型架构。我们的批处理策略有一个额外的优势，它允许我们将批处理大小的数量缩小到少数几种情况。这意味着工作负载几乎是静态的：一批标记与任何其他批次相同。

一旦你有了这个焦点，在GPU上优化LLM与每个软件优化项目的形状相同：将工作负载分解为其组成步骤；测量每个组件的时间；将这些时间与理论理想进行比较；按每个组件可以实现的端到端加速排序；处理第一个；然后回到开始。它们可能是老式的，但电子表格是你的朋友！

考虑我们对Llama 3.1 8B模型批处理时间的分解。这些数字是针对4个NVIDIA H100 GPU，模型采用FP8精度，批处理大小为2048，KV缓存中有8192个标记。

![时间花费分解图](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67e6f576d569bd3b2a52ced8_chart-time-spent.webp)

值得注意的是，几乎3/4的总时间花在矩阵乘法和自注意力上，这些受GPU的数学吞吐量（FLOPS）限制。然而，仍有改进的空间！至少20%的时间仍然在可以优化掉的工作上。

我们优化旅程的亮点包括：

* **CUDA Graphs**：现代GPU对推理工作负载如此之快，它们可以_执行_工作的速度比主机CPU_提交_工作要做的速度更快。因此，你会看到执行中的空闲间隙，GPU正在等待接收下一个工作单元。解决方案是预先定义_所有_要做的工作并一次性提交。在NVIDIA GPU上，这种技术称为CUDA Graphs。专注于固定大小的批处理使这变得更容易，因为即使请求大小发生变化，工作负载也永远不会改变。

* **FP8**：最新的GPU支持8位浮点值的2倍更快的数学吞吐量（与16位浮点相比）。这通常称为"权重和激活"量化，因为你将模型权重和运行时值都转换为8位浮点。

* **FlashAttention-3**：FlashAttention项目激励了开源社区完善硬件优化的自注意力实现，这些实现比以前的替代方案_快得多_。我们很幸运能与[Colfax International](https://www.colfax-intl.com/)的专家合作，为推理情况完善最新的FlashAttention-3版本。结果是我们所知道的最快的自注意力实现。

* **高效通信**：在多GPU执行中，GPU之间经常进行集体通信以共享它们都需要的分割激活。这个时间是纯粹的开销，所以我们转向all\_reduce和all\_gather的自定义实现（基于[TRT-LLM](https://github.com/NVIDIA/TensorRT-LLM)中的实现），这些实现比开箱即用的NCCL实现具有更低的推理延迟。

* **自定义CUDA内核**：在优化了矩阵乘法和自注意力等大事项后，你会剩下花在杂项上的开销时间...东西。这种开销的最佳修复之一是_内核融合_：将多个操作组合到一个函数中，这样你就不必反复加载激活。我们为各种融合编写了自己的CUDA内核，这些融合太复杂，无法用原生PyTorch功能表达。

## 总是有更多可以优化的地方

对于代码补全和聊天，有一个延迟阈值，低于该阈值用户会体验到产品"反应灵敏"。一旦我们达到这个神奇的阈值，我们可以停止优化吗？

不幸的是，有多个额外延迟源会影响用户体验：网络、请求排队和从百万嵌入中检索加起来是一个显著的时间量，特别是在请求延迟的较高百分位数。为了给我们的用户提供最佳体验，我们必须学会如何提供上下文丰富的代码补全，首个标记时间低于120毫秒，同时保持我们GPU的FLOPS利用率超过25%。我们认为这是一个杰出的数字，考虑到即使是高度优化的训练作业也只能达到38%到43%的FLOPS利用率（[Llama 3.1论文，第10页](https://arxiv.org/pdf/2407.21783)）。

今天，我们的总补全延迟保持在220毫秒以下，为灵敏的代码补全设定了新标准，同时在每次按键时提供完整的代码库感知。

然而，我们相信总是有更多可以优化的地方，最好将推理优化视为提供选项：任何推理加速都可以被视为一个可以再投资以为用户创造价值的预算。例如，我们可以将这个预算用于增加模型大小或添加更多上下文，以进一步提高对大型代码库的理解。

还有很多可以讨论的内容，我们计划在未来的博客文章中讨论额外的挑战。例如，我们内部开发推理堆栈的策略使我们能够优化模型以重用缓存，节省大部分工作。

如果LLM推理和构建最具上下文感知的代码AI让你兴奋，[让我们聊聊吧！](https://www.augmentcode.com/careers)我们正在寻找在优化AI驱动开发未来方面茁壮成长的工程师。要体验深度上下文、低延迟AI编码的好处，[注册Augment](https://www.augmentcode.com/free-trial)。

---

**关于作者：**  
**Markus Rabe** 是一位工程师和研究员，在机器学习、自动推理和形式化方法领域发表过多篇论文。在加入Augment之前，他在Google Research与Christian Szegedy共同担任技术负责人，负责[Memorizing Transformers](https://arxiv.org/abs/2203.08913)和[Flash Attention](https://arxiv.org/abs/2112.05682)的前身等工作。

**Carl Case** 是一位研究工程师，过去十年一直致力于通过在加速硬件上扩展训练来改进深度学习系统。作为ML研究员，他在百度硅谷AI实验室从事大规模DL语音识别工作。在加入Augment之前，他在Nvidia从事硬件架构工作，并开发了混合精度训练技术以及改进的GPU间通信，用于集群规模训练。
