# 值得信赖的软件代理

**作者：** Scott Dietzen  
**日期：** 2025年4月2日

## AI代理的承诺与现实

每位工程领导者都希望倍增团队的影响力。软件代理承诺将每位工程师转变为技术主管，指挥一支自主AI团队处理最繁琐、重复且无趣的任务。但承诺与现实之间存在着关键差距……

当代理误解我们的意图、无法完成任务，或更糟——破坏现有系统时，它们就会成为负担而非资产。正如蜘蛛侠的叔叔所说，"能力越大，责任越大"——对于管理着数千个文件和数百万行代码的软件团队来说，任何低于专家级的表现都是不可接受的。

## 为什么上下文是缺失的关键要素

对于大规模软件工程，上下文不仅仅是有帮助的——它是必不可少的：

* **架构一致性**：确保新代码与现有系统设计、依赖关系、策略和最佳实践保持一致。
* **代码复用**：改编现有软件而非引入更多冗余逻辑。
* **根本原因分析**：在复杂的相互依赖系统中隔离故障。
* **端到端实现**：随着提议的代码变更一起更新文档、测试、脚本等。
* **CI/CD故障排除**：解决复杂的构建流水线问题。

虽然大多数AI编码助手在生成全新代码方面表现出色，但在大型、成熟的代码库中工作时却表现不佳——而这恰恰是大多数企业工程时间所花费的地方。

## Augment Agent：为软件工程而生

Augment成立于2022年，旨在帮助开发者驾驭构建和演进大型、复杂代码库的复杂性，这一焦点至今仍然独特。Augment代理是功能上的下一个量子级飞跃。我们的代理架构已通过[SWEBench上的最佳成绩](https://www.augmentcode.com/blog/1-open-source-agent-on-swe-bench-verified-by-combining-claude-3-7-and-o1)得到验证，这是软件工程能力的行业基准。但更令人印象深刻的是，我们实现了自举——Augment代理编写了我们大部分工具集成代码和测试，并进行了重大优化。

数千名开发者在过去几个月里一直在利用我们代理的早期访问权限。现在，我们将Augment代理提供给所有人。

## 工程级代理的要求

我们构建了Augment的代理平台，以提供您期望从最值得信赖的高级工程师那里获得的能力：

### 深度代码库理解

我们的代理不仅仅看到文件——它们理解您的整个工程生态系统：代码库历史、文档、工单、工程对话、屏幕截图、UI设计和代码审查。随着代理自主承担更困难的任务，专业知识变得更加重要。模型已经足够好，可以利用这些各种数据源，但编码AI必须明智地提供恰到好处的上下文来完成手头的工作。（对于任何有意义规模的代码库，不可能将其全部作为上下文传递。）经过三年的研发，Augment为每项任务提供精确的上下文，找出您数百万行代码库中细微的"魔鬼细节"。

### 自适应学习

Augment代理随着时间的推移学习您的工作习惯、偏好和风格，使它们的贡献越来越符合您的期望。这些_记忆_可以由我们的代理自动添加，通过点击记忆按钮，直接要求您的代理记住某些内容，或直接编辑记忆文件。（这一功能类似于我们帮助Augment理解[Next Edit中的人类意图](https://www.augmentcode.com/blog/the-ai-research-behind-next-edit)的工作。）

### 无缝工具集成

我们的代理框架支持模型上下文协议（MCP），并包括与领先开发者工具（如Web搜索、GitHub和Linear）的原生集成。与客户端解决方案不同，我们基于云的集成可以在整个团队中安全共享。

### 来自IDE的灵活指导

Augment提供两种不同的操作模式：

* **手动模式**：当代理需要执行命令或访问集成时会暂停，允许您审查和批准每个操作。
* **自动模式**：代理独立实现整个计划，自动编辑文件、执行终端命令和访问外部工具。

### 轻松回滚

Augment在代理工作时自动创建工作区快照。这使您能够在审查更改的同时持续进行，并可以通过一键点击恢复到任何先前的检查点。

### 对变化软件的一致视图

当多个开发者或代理在同一代码库上工作时，Augment会持续调整其[代码库的实时索引](https://www.augmentcode.com/blog/a-real-time-index-for-your-codebase-secure-personal-scalable)以纳入最新更改，确保每个人都能看到软件的精确版本（即，在特定分支上工作的人类和代理看到的代码与不在该分支上的人看到的代码不同）。

## 超越代码生成，迈向真正的软件工程

虽然我们的竞争对手专注于氛围编码/0→1端的领域，但Augment解决的是大型代码库中软件工程更具挑战性的现实。大多数AI编码工具擅长生成独立的代码片段或全新项目，但在面对现有系统内在的复杂性时却表现不佳。我们的客户报告说，Augment代理在以下复杂场景中表现特别出色：

* 实现跨越多个服务和代码库的变更
* 使旧代码适应新需求
* 加速跨系统边界问题的调试
* 确保关键系统组件的全面测试覆盖

## 前进的道路

毫无疑问，使用代理有一个学习曲线，但随着它们获取更广泛的上下文、学习并受益于底层模型中不断改进的推理能力，它们将改变软件开发的各个方面。对于大型代码库的软件工程，最好的开始方式是给代理一个增量的、明确定义的任务，然后从那里开始构建。

AI已经在推动新软件的创建，但更重要的是，它现在准备通过提高质量、增强功能和为软件工程的辛劳带来乐趣，来影响我们现有的大型软件系统。Augment正在引领这一旅程，我们欢迎您加入我们！

---

**关于作者：**  
在加入Augment Code之前，"Dietz"曾担任Pure Storage的CEO。在他的领导下，Pure从0增长到超过10亿美元的收入，从15名员工增长到数千名员工，并成功上市。作为4次创业者，他获得了卡内基梅隆大学计算机科学博士学位，专注于机器学习。
