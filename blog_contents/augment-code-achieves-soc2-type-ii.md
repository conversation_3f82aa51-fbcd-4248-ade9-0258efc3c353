# Augment Code achieves SOC2 Type II

**Author:** <PERSON>  
**Date:** September 5, 2024

We are excited that Augment Code has achieved SOC2 Type II attestation! This is a major milestone for us, and we want to share our approach to security, privacy, and compliance.

## AI and Privacy: You Can Have Both!

One of the biggest challenges when adopting AI is ensuring data privacy and security. AI can make software development much more efficient, but it often raises important questions like:

* What happens to your data?
* Who has access to your code and information?
* Under what conditions can companies access your data?
* Will your data be used to train future AI models?

From day zero, we have built Augment Code with a strong focus on security and privacy.

## How Does Augment Code Approach Security?

Our customer's intellectual property will never be used to train AI models, whether by us or any third party. Our engineering team is dedicated to building strong security and privacy protections directly into our platform.

Here's how we do it:

* **Strict Access Control:** AI suggestions are only informed by code that the specific engineer can access.
* **Always-On Security:** Our product is designed with security features that are always on, so you don't need to worry about setting them up yourself.
* **Independent Validation:** We hire independent experts to test our security systems regularly and ensure they meet our high standards.
* **Customer Collaboration:** We work closely with our customers to improve our security measures.

## Where is Augment Code Now?

On July 10, 2024, we were proud to receive our SOC2 Type II attestation, which confirmed that we met our rigorous security, privacy, and operational excellence standards. This attestation came after a thorough three-month audit from April 1 to July 1, during which we did not have a single issue. This achievement sets us apart, especially as other AI tools, like GitHub CoPilot, have yet to reach this level of SOC2 Type II.

## What Makes Augment Code Different?

Security, privacy, and compliance are at the heart of everything we do.

Our team includes experts from top tech companies like Apple, Google, Snowflake, Meta, and Databricks. With their experience, we designed Augment Code to prioritize security from the start.

Here's what sets us apart:

* **Data Minimization:** We only collect the data necessary to provide our services.
* **Multiple Layers of Security:** We use several layers of security to protect against cyber threats.
* **Least Privilege Principle:** We limit access to our systems, giving it only when absolutely necessary to reduce security risks.
* **Automation:** We automate key processes to improve efficiency and security.
* **Fail-Safe Design:** Our system is built to automatically protect your data and functionality in case of any issues.

At Augment Code, security and privacy were not added as an afterthought; they were built into our platform from the ground up. We continually validate our commitment to these principles through third-party attestations like SOC 2 Type II. [**Sign up**](https://www.augmentcode.com/waitlist) for access to Augment Code and bring your team the AI coding platform that delivers team efficiency, developer happiness, and platform security.

---

**About the Author:**  
Prior to Augment Code, "Dietz" served as CEO of Pure Storage. Under his leadership, Pure grew from 0 to $1B+ in revenue, 15 to thousands of employees, and had a successful IPO. A 4x entrepreneur, he earned a Ph.D. in Computer Science with an emphasis on Machine Learning from Carnegie Mellon University.
