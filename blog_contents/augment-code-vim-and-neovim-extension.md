# Augment 💚 Vim

**Author:** <PERSON>  
**Date:** February 14, 2025

Developers shouldn't have to switch editors to harness the power of AI. <PERSON><PERSON> and Neovim developers can now get Augment's powerful code completions and chat–yes, chat!–right in their editors. To get started with Augment, just [install the extension](https://docs.augmentcode.com/setup-augment/install-vim-neovim), sign into Augment, and get back to coding in under a minute. If you're new to Augment, you can [sign-up for free](https://augmentcode.com/install-now) now.

## **Lightning fast completions and chat** 

You can stay in the flow with in-line completions that truly understand your codebase. With chat you get instant answers about your work instead of stopping to search documentation, debug an issue, or interrupt a teammate. Dig deeper with follow-up questions or iterate on a solution, all in the comfort of your editor.

## **Powerful Context Engine**

Augment delivers real-time, deep understanding of your code to every completion and chat automatically. When your coding assistant knows your code, architecture, dependencies, style, and best practices you can make complex changes quickly and with confidence.

## **Once you start, you won't `:q!`**

If you've ever watched an experienced vim user in the flow, it might look like they have superpowers. With Augment's Context Engine automatically powering every keystroke, now they really do.

[Sign-up for a free 30-day trial of Augment](http://augmentcode.com/install-now), your [hjkl](https://hjkl.ai) keys will thank you.

---

**About the Author:**  
Chris is the head of developer experience, working across Augment to make building software better for every developer. Chris has been making developers happier and more productive for 15 years at innovative companies like New Relic, GitHub, Salesforce, and FireHydrant. You can find him at @amateurhuman everywhere on the internet.
