# Augment Code raises $227 Million to empower software teams with AI

**Author:** <PERSON>  
**Date:** April 24, 2024

_Investors Include Sutter Hill Ventures, Index Ventures, Innovation Endeavors, Lightspeed Venture Partners and Meritech Capital_

PALO ALTO, Calif. — APRIL 24, 2024 — AI coding assistance startup Augment Inc. emerged from stealth today and announced its $227 million Series B round at a $977 million post-money valuation with investments from Sutter Hill Ventures, Index Ventures, Innovation Endeavors, Lightspeed Venture Partners and Meritech Capital. This brings Augment's total funding to $252 million, following its $25 million Series A led by Sutter Hill Ventures. The latest capital infusion will be used by Augment to accelerate product development and build out its product, engineering and go-to-market functions as the company gears up for rapid growth.

The company is founded by <PERSON>, former chief architect at Pure Storage and software engineer at Microsoft, and <PERSON>, an AI researcher who hails from Google. It is led by industry veterans <PERSON>, whose previous leadership experience includes Pure Storage, Yahoo! and WebLogic / BEA Systems, and <PERSON>, an alumnus of Google, Shopify, Mozilla and Palm. Augment's engineering team boasts deep AI and systems expertise, with alumni from Google, Meta, NVIDIA, Microsoft, Databricks, Snowflake, VMware and Pure Storage.

Sutter Hill Ventures Managing Director <PERSON> said, "Augment Code has built a truly brilliant team, among the best in enterprise AI, and as good as any team we have ever helped put together. They have a stellar track record of delivering best-in-class solutions and leading companies from early stage to category leader to IPO. We are already seeing great things from Augment, and we are just getting started."

Over $1 trillion is spent on software engineering annually, according to [**Gartner**](https://www.gartner.com/en/newsroom/press-releases/2023-10-18-gartner-forecasts-worldwide-it-spending-to-grow-8-percent-in-2024), yet most companies remain dissatisfied with the programs they produce and consume. Software is too often fragile, complex and expensive to maintain with development teams bogged down with long backlogs for feature requests, bug fixes, security patches, integration requests, migrations and upgrades. AI is hailed as the long-awaited remedy, with [**Gartner predicting**](https://www.gartner.com/en/newsroom/press-releases/2023-11-28-gartner-hype-cycle-shows-ai-practices-and-platform-engineering-will-reach-mainstream-adoption-in-software-engineering-in-two-to-five-years) that "by 2027, 50% of enterprise software engineers will use ML-powered coding tools." However, current AI coding assistants don't adequately understand the programmer's intent, improve software quality nor facilitate team productivity, and they don't properly protect intellectual property. In fact, a 2023 Gartner report on [**AI and developer experience**](https://www.gartner.com/en/documents/4480999) found that tools like pair programming currently only offer "incremental, quality-of-life improvements to developers rather than significant boosts in productivity."

As Eric Schmidt, Founding Partner at Innovation Endeavors and former CEO of Google explained, "Software remains far too expensive and painful to develop. AI is poised to transform coding, and after surveying the landscape, we came away convinced that Augment Code has both the best team and recipe for empowering programmers and their organizations to deliver more and better software."

"Software engineering is rapidly evolving into a collaboration between human and artificial intelligence," observed Igor Ostrovsky, co-founder of Augment Inc. "At Augment Code, we are working with extraordinary focus to empower teams around the world with AI, enabling them to build better software."

"As large language models emerged a few years ago, this team saw their potential to make programming dramatically more productive and yield much higher-quality software," said Scott Dietzen, CEO of Augment Inc. "Current AI for code platforms are falling well short of realizing the potential of generative AI, and we are thrilled to be launching a product to close this gap!"

CEO of Keeta, Ty Schenk, explained, "Augment is solving real world engineering challenges with their contextual awareness of our code base. We are seeing a >40% increase in developer productivity across the board."

"AI has the potential to make software developers 10x more productive, but achieving this requires deep research into AI for code coupled with advanced systems engineering," shared Guy Gur-Ari, co-founder of Augment Inc. "It's exciting to be part of a team that is uniquely positioned to deliver on this promise, and truly transform the way software development gets done."

"The best software comes from augmenting developers, not replacing them. We believe that AI technology can help developers stay focused on the joy of coding while eliminating the toil that can slow progress to a crawl. To achieve this vision, the AI needs to have an expert understanding of codebases, operate at the speed of thought, support teams instead of just individuals, and carefully protect intellectual property," explained Dion Almaer, VP of product at Augment Inc. "As someone who has dedicated my career to meeting the needs of software engineers, I am confident that Augment is poised to deliver this new experience and revolutionize software development at scale."

Dietzen continued, "It's clear we're entering a once-in-a-generation pivot point in software development. At Augment, not only do I get to work with many of the brightest minds in AI, we're fortunate to have the backing of the world's leading investors to fuel our vision. Augment's AI platform naturally and seamlessly empowers every developer."

To learn more about Augment and secure your spot to transform your coding:

Visit [**www.augmentcode.com**](https://www.augmentcode.com/)

Join the waitlist for Augment's [**Early Access Program**](https://www.augmentcode.com/waitlist)

## Additional Investor Quotes

> We are strong believers in Augment's potential to not only eat the code assistant market, but redefine how software engineering is performed at scale!  
> \- Shardul Shah, Partner, Index Partners

> At Lightspeed, we believe there's a massive market opportunity for AI to transform software development. Augment's exceptional team seamlessly empowers software engineers to deliver higher quality code far more easily, quickly and collaboratively. Lightspeed is thrilled to be joining Augment to help make the world's software better.  
> \- Ravi Mhatre, Founder and Managing Director, Lightspeed

> Augment represents Sutter Hill Ventures' first big bet on AI. This is an extremely talented team that marries the best in systems and cloud programming with unique breadth and depth in AI research. Great things are coming soon.  
> \- Palmer Rampell, Managing Director, Sutter Hill Ventures

> Meritech is thrilled to be backing an extraordinarily talented Augment team. In our due diligence, we found Augment's depth and breadth of AI talent to be greater than that of all the other startups in the space combined.  
> \- Alex Clayton, General Partner, Meritech Capital

## About Augment Inc.

Founded in 2022, Augment Inc. emerged from stealth in 2024 to bring AI coding assistance innovations to developers and software teams. The company is backed by Sutter Hill Ventures, Index Ventures, Lightspeed Venture Partners, Innovation Endeavors and Meritech Capital. Augment's expert understanding of each codebase and dependencies removes the toil in developers' workdays, so teams can once again experience the joy of coding.

---

**About the Author:**  
Prior to Augment Code, "Dietz" served as CEO of Pure Storage. Under his leadership, Pure grew from 0 to $1B+ in revenue, 15 to thousands of employees, and had a successful IPO. A 4x entrepreneur, he earned a Ph.D. in Computer Science with an emphasis on Machine Learning from Carnegie Mellon University.
