# Augment is free for open source

**Author:** <PERSON>  
**Date:** December 4, 2024

**TL;DR** Augment is free for developers that want to contribute to and maintain open-source projects. Augment's deep codebase understanding tears down some of the biggest pains for working on open-source projects: the context you need to contribute and the help you need to make quality PRs. Sign up for your free account at [augmentcode.com/opensource](https://augmentcode.com/opensource).

## Get up to speed, contribute more

Being a new contributor to any software project is hard. You are usually on your own to learn the ins-and-outs of the project–the internal structure, dependencies, patterns and practices, and all the quirks and gotchas that came before you. With Augment you can ask questions, get suggestions, and automatically pull in internal modules and functions you might not know existed, making it the ideal developer AI for unfamiliar code.

Today, we are making [Augment free to maintainers and contributors](https://augmentcode.com/opensource) to make working on open source better for everyone. No rate limits or out-of-date models, you'll have the full power of Augment at your fingertips to make a contribution or stay on top of pull requests.

## The right code, not just more code

The last thing a project maintainer needs is another pull request that doesn't adhere to the project's style and practices. Augment helps contributors first get up to speed on the codebase and then helps them craft a change that fits right into the project. The less back-and-forth that needs to happen on the pull request, the more time is saved for both the maintainer and contributor–and ultimately the more code gets merged.

> "Augment Code has the best code RAG I've tried, and I've tried everything I can find. It has at least a 50% hit rate in Cassandra, a huge win for a codebase that complex. Augment has saved me a ton of frustration."  - Jonathan Ellis, co-founder and CTO, DataStax and Apache Cassandra committer

## Working on open source just got easier

Once you're [registered through our open source program](https://augmentcode.com/opensource), you'll be able to use Augment for free. Download the extension for [Visual Studio Code](https://marketplace.visualstudio.com/items?itemName=augment.vscode-augment) or [JetBrains](https://plugins.jetbrains.com/plugin/24072-augment), sign in, and open your favorite open-source project. Ask Augment to explain the codebase or dive right in and start coding with context-aware completions.

## The fine print

By registering with the open source program, you are allowing Augment to train our models on your data including the code in your workspace and your product usage. You'll have access to Augment for free and be included in our early access program to experience our latest innovations before they're generally available.

Whether you've never contributed to open source or your contribution graph is solid green, we think working on open source is better with Augment. See all the program details and [sign up at augmentcode.com/opensource](https://augmentcode.com/opensource).

---

**About the Author:**  
Chris is the head of developer experience, working across Augment to make building software better for every developer. Chris has been making developers happier and more productive for 15 years at innovative companies like New Relic, GitHub, Salesforce, and FireHydrant. You can find him at @amateurhuman everywhere on the internet.
