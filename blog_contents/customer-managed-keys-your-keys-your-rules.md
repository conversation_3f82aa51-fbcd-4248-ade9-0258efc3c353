# Customer Managed Keys: Your keys, your rules

**Author:** <PERSON>  
**Date:** April 16, 2025

Building software with AI is a massive competitive advantage. But how can we adopt AI without blowing up our security requirements? 

At Augment, we've seen teams fly through code generation and auto-complete performance benchmarks—only to hit a wall during procurement or legal review. Not because of bugs or latency—but because of one simple question:

"**Who controls the data?**"

Today, with the launch of Customer Managed Keys (CMK), we're giving enterprise teams a clear answer: you do. [Our comprehensive security practices](https://www.augmentcode.com/security) have always prioritized data protection, and now CMK takes this commitment to the next level."

## Why CMK Matters

Augment runs in the cloud for a reason: it's the only place where AI moves fast enough. You get the newest models, the fastest patches, and the performance needed to actually help your team ship code—not just demo it.

But that cloud-first advantage used to come with a tradeoff: your security team had to trust us with access to your data. Not anymore.

With CMK, your data is protected with a symmetric key **you manage in your own environment**. If access to the key is ever revoked, **Augment loses access to your data**—full stop.

No tickets. No calls. No sweat.

## Built for the Realities of Enterprise Engineering

CMK is available exclusively to customers on the [Enterprise Tier,](https://www.augmentcode.com/pricing) and it's purpose-built for teams scaling secure AI adoption without relaxing their standards:

* **Hard boundaries, built in**  
  Your key lives in your cloud. Augment can't read encrypted data without it.
* **No GPU wars, no local installs**  
  You still get fully managed, low-latency AI in the cloud. No hardware overhead. No local software to secure.
* **Zero friction with security**  
  CMK satisfies internal requirements for encryption ownership, auditability, and data lifecycle control—before your security team even asks.

## When You're Ready to Upgrade, CMK Is Ready Too

We built CMK because our most mature customers demanded it—and our future customers will need it.

If your team is growing and you're preparing to roll out Augment at scale, CMK can help you avoid long review cycles, blocked security approvals, or worse—being forced to choose between "speed" and "standards."

You can have both.

Want to see how it works? [Reach out.](https://www.augmentcode.com/contact) When you're ready to level up, we'll meet you there.

---

**About the Author:**  
Igor dove head-first into generative AI in 2021 as Sutter Hill Ventures' Engineer in Residence, leading to the founding of Augment Code. Before this, as a Chief Architect at Pure Storage, he led the technical development of FlashBlade to $2B lifetime sales. His earlier experiences included a 6-year tenure at Microsoft and reaching the ACM ICPC World Finals in 2007.
