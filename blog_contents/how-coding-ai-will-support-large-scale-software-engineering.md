# How coding AIs will support large-scale software engineering

**Author:** <PERSON>  
**Date:** January 7, 2025

_TL;DR:_ _Large, long-lived software projects are essential for human endeavor, but profoundly hard to craft and evolve. Today's coding AIs come up well short of solving the real pain points of software engineering. Augment Code is empowering teams to overcome these challenges — from inspiration to software excellence easily and quickly._ 

Programmers and the companies that employ them are grappling with how AI is going to impact software engineering. Will [AI bring joy back to programming](https://www.augmentcode.com/blog/reimagining-software-engineering-through-ai) or [destroy software jobs](https://nypost.com/2024/08/21/business/amazon-software-engineers-could-stop-coding-soon-due-to-ai/)? Are we [in a bubble](https://www.forbes.com/sites/bernardmarr/2024/08/07/is-the-ai-bubble-about-to-burst/), [advancing humanity](https://www.nobelprize.org/all-nobel-prizes-2024/), or a mix of both? 

**Software engineering is hard.** I have spent my career working with very talented teams looking after large repositories of sophisticated code, and no matter what your conviction about AI's potential impact, we can agree that developing software at scale is extraordinarily difficult. We have made substantial advances over the years—higher level languages, integrated development environments (IDEs), continuous integration & delivery (CI/CD), open source & public cloud. Yet software still generally comes up short of our aspirations: hard to use, fragile, insecure and missing capabilities. I have never encountered a project that does not have a long wishlist of feature requests and refactoring improvements. 

Moreover, as software projects scale and mature, enhancing them gets far more challenging. Today's codebases can be huge — open source Chromium, for example, has over 30m lines of code (LOC), [Uber maintains over 90m LOC](https://www.uber.com/blog/nilaway-practical-nil-panic-detection-for-go/), and Google has other repos that contain [more than 2B LOC](https://www.youtube.com/watch?v=W71BTkUbdqE) — making them supremely difficult to navigate and understand let alone modify. Over time, software tends to accumulate technical debt, making it more brittle. Modern programs often depend on a complex web of interconnected data stores, libraries and services, that are each themselves in flight. Programs are now very likely to be distributed, leading to far more potential points of failure from which the software is expected to recover, while also exacerbating performance optimization and security. Big projects require big teams, often working across different timezones, and participation evolves as new developers ramp up and others move on. 

As a result, software today is far more expensive to develop and to maintain, and [quality may actually be in decline](https://stackoverflow.blog/2023/12/25/is-software-getting-worse/). One estimate of the annual cost of [software failures to the US economy alone is $2.4 trillion](https://www.it-cisq.org/the-cost-of-poor-quality-software-in-the-us-a-2022-report/). Broken software cannot [eat the world](https://a16z.com/why-software-is-eating-the-world/)**.** 

## AI is a dramatic leap forward for software engineering productivity

Our thesis is that coding AIs that complement developers are already improving productivity more than prior leaps forward: because of their natural language capabilities, AIs can work at whatever level of abstraction a programmer chooses.

One study determined that GitHub Copilot enabled developers [to complete tasks 55% faster than without AI](https://visualstudiomagazine.com/articles/2024/09/17/another-report-weighs-in-on-github-copilot-dev-productivity.aspx#:~:text=The%20group%20that%20used%20GitHub,didn't%20use%20GitHub%20Copilot.). Now a Fortune 500 customer that switched from Copilot to Augment Code has achieved an additional 40% improvement over and above what they had already achieved with Copilot. That productivity boost reflects the difference between an AI that understands basic programming languages and algorithms (think new C.S. grad), and one like Augment that deeply understands your codebase (think an expert from your team). 

## 7 essential beliefs for delivering an AI for software engineering

At Augment, we're building an AI platform that improves software quality and the practice of software engineering _at scale_ — we are innovating to help you successfully improve the largest repos of your most complex code! 

Here are a few of the core beliefs that guide how we think about solving this problem for professional software teams:

**Context is everything.** The knowledge enshrined in programs is hugely valuable: user interface, APIs, data model, architecture, security policies, performance optimizations, failure recovery, and so on, all often incrementally improved over years. But today's coding AIs broadly lack expertise in your codebase and in how you build software. Augment instead strives to bring _all_ of the knowledge embodied in your code, documentation, and the supporting ecosystem to bear in helping you make your software better. Having always-on rich context makes Augment's AI feel far more natural, obviating the need to engineer prompts or correct hallucinations. 

**Ramping on unfamiliar code is a hidden, unsolved problem:** The biggest hidden cost in software development is the time engineers spend [trying to understand code that isn't theirs](https://nicholas.carlini.com/writing/2024/how-i-use-ai.html): Waking up at 2am to a Sev-0 caused by code you didn't write, frantically trying to understand it. Or, being the infrastructure and security engineers reviewing hundreds of PRs by engineers on other teams, on all parts of your codebase. Because Augment deeply understands your software, we dramatically accelerate your productivity ramp, providing insights whenever and wherever they are needed, as well as [reduce or even eliminate the mentoring load](https://www.augmentcode.com/blog/pigment-automates-boilerplate-tasks-and-simplifies-testing-with-augment-code) on senior engineers to onboard new team members.

**Improving quality is more important than proliferating code.** Naive coding AIs are all too happy to add redundant code to your codebase. Given its deep contextual awareness, Augment is better able to propose reusing code or _even deleting code_. Similarly, Augment better understands your application architecture, data model, best practices, coding conventions and so on, allowing us to deliver higher-quality suggestions, or even helping you port subsystems to C++ or Rust to improve performance. Moreover, Augment can recognize trendlines, helping to accelerate a desired evolution of your codebase, such as transitioning to improved algorithms, databases or third-party libraries.

**In large systems, there's no such thing as a simple change - but AI can handle that complexity for you.** If programming were building a toy airplane, then software engineering is constructing an Airbus A380 and continually remodeling it in flight. Every change ripples through complex systems in ways humans struggle to fully grasp. Augment's Next Edits is one of the first agents to map the ramifications of a proposed code modification across an entire complex repository. While today we're helping with workflows, as well as accelerating tests and migrations, the winning AIs of tomorrow will shoulder even more of this complexity burden.  

**Software development happens beyond the IDE.** Application development is rarely an individual sport, yet today's coding AIs tend to focus exclusively on individual productivity. Augment instead strives to meet your team where they work. Consider this recent exchange within Augment's internal development: An engineer raises a question about an error message on Slack. Another adds Augment to that thread to get our AI's take. Augment leverages its deep codebase awareness and other context and responds in Slack that this looks like a bug, and the original engineer assigns a PR to make the fix suggested by Augment.  
  
**_Augmenting_ rather than replacing developers.** Humans excel at the complex reasoning necessary to evolve software systems over the longer term: Should we move this program to the cloud? Should we switch to a micro-services architecture? How much would caching improve the user experience and reduce load on the backing database? Software engineering decisions like these remain out of reach for today's AIs. At the same time, engineers do less well at managing the details associated with a complex refactor or upgrade, or grinding through the repetitive tasks essential for business-critical software (unit tests anyone?).  
  
So then, human insight will continue to shape the software lifecycle, while machine intelligence reduces the toil that plagues engineering teams, allowing them to focus more on creative problem-solving, product quality and customer delight. Super intelligence (what Dario Amodei in a great essay calls ["powerful AI"](https://darioamodei.com/machines-of-loving-grace)) may someday disrupt this balance, but we at Augment believe that is still many years off. In the meantime, platforms like Augment dramatically accelerate the journey from novice to expert software engineer as well as making the trip far less painful. 

**AI will actually increase demand for software engineers.** What if you could deliver _all_ of the features on your backlog while at the same time refactoring to eliminate your technical debt? Today, companies artificially cap their software investment due to unpredictable returns - features arrive slowly, quality suffers, budgets run over, and security remains a constant concern. But when AI dramatically improves the productivity and reliability of software development, the economics fundamentally change. Organizations will expand their software initiatives because they'll finally get consistent, reliable returns on their investment. This increased predictability and output won't reduce demand for engineers — instead, it will unlock previously untapped business opportunities that require even more engineering talent to make those software dreams come true. Indeed, the optimistic view is that material economic growth will be unleashed with dramatically improved software, actually driving increased demand for engineering talent.

**See what Augment can do for you and your team.** _AI is elevating the discipline of software engineering, and our mission at Augment Code is to tame the complexity of crafting and evolving the software that runs the world._ We want to build that better future with you. [Please join us!](https://www.augmentcode.com/waitlist)

---

**About the Author:**  
Prior to Augment Code, "Dietz" served as CEO of Pure Storage. Under his leadership, Pure grew from 0 to $1B+ in revenue, 15 to thousands of employees, and had a successful IPO. A 4x entrepreneur, he earned a Ph.D. in Computer Science with an emphasis on Machine Learning from Carnegie Mellon University.
