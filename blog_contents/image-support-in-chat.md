# Image support comes to Augment Chat for VS Code

**Author:** <PERSON><PERSON><PERSON>  
**Date:** March 19, 2025

There's a reason developers love a whiteboard: sometimes, the only way to get your message across is with pictures, not words. We're excited to share that Augment now supports inline images in Chat—available today in VS Code, with IntelliJ support coming soon.

Unlike other tools that simply attach files as separate entities, Augment seamlessly embeds images directly within your conversation flow. It's like giving your AI assistant a set of eyes to see exactly what you're working on by sharing screenshots, diagrams, or any visual content directly in-line with your chat.

## What You Can Do:

* **Communicate complex architectures** - Share system diagrams and get insights with perfect context
* **Accelerate UI development** - Drop in mockups and Augment will suggest implementations using your existing components
* **Squash visual bugs faster** - Show Augment exactly what's happening instead of struggling to describe it
* **Design pattern implementation** - Upload diagrams and get code structure suggestions that match your codebase conventions
* **Build faster and more creatively** - We look forward to seeing how support for images improves your workflows  

![Screenshot of Augment Chat with image support](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67e45cefd5c2b0f71ade1ace_CleanShot%202025-03-19%20at%2017.20.53.webp)

## FYI: Things you should know (and things we're working on)

* Images are automatically scaled to 1024px (larger images may show some compression)
* Shareable chat links don't display images yet (but they will soon!)
* PDF support is in the pipeline
* IntelliJ support is on the way  

Ready to dive in? Just grab the [latest version of the Augment extension](https://marketplace.visualstudio.com/items?itemName=augment.vscode-augment) and you're good to go! Join our [awesome Discord community](https://www.augmentcode.com/discord) to share your experiences, creative ideas, and yes - even your favorite memes.

Happy coding!

---

**About the Author:**  
Zheren Dong is a software engineer at Augment developing systems for chat. Previously, he built data infrastructure for vehicles at Applied Intuition, firmware for RISC-V chips at Rivos, and optimized Ads systems at Alibaba. With a Master's from UC Irvine and Bachelor's from UC Santa Barbara in Computer Science, Zheren brings expertise in scalable, high-performance systems architecture for complex software challenges.
