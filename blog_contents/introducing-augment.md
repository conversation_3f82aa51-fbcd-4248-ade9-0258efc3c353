# Introducing Augment: a company dedicated to empowering developers with AI

**Author:** <PERSON>  
**Date:** August 1, 2024

I'm incredibly excited to share that Augment, the company I joined to help empower developers, has come out of stealth.

With a lot of FUD around AI taking all of the knowledge worker jobs, including those of developers, I believe it is important to get across the counter argument:

> [**_Don't fire <PERSON> for <PERSON> just yet. Augment Kevin with super powers!@dalmaer_**](https://x.com/dalmaer/status/1778955486554955855)

If you think about what software engineers **actually do** and what AI excels at, you should reach the same conclusion. It's easy to anthropomorphize AI tools, especially when you're chatting with them and considering their portrayal in science fiction. With that in mind, I believe in creating systems that resemble J.A.R.V.I.S more than HAL.

As we develop these systems, it's essential to remember that humans and computers have unique strengths. The real magic happens when humans take charge, supported by ever-present, fully connected computer systems.

By doing so, we can not only improve life for developers individually, but also empower teams and organizations to accomplish much more with reduced toil and communication costs.

I'm passionate at doing my part to help here, and I want to share my journey to Augment with you.

## Seeing the future of software development

I love programming. Whenever I write some code, it tends to be a good day. There is something about the creative process that ends with something tangible that is good for my brain. Any platforms, tools, or services that allow me to stay in that certain flow of development become favorites. There is an art to taking an idea, breaking it down, and making progress.

The longer I am on the path to running code that works – or getting effective help back onto the path when it isn't working – the better I feel.

On the flip side, whenever I am doing something that feels like toil, or I feel really stuck, the worse I feel.

There have been a couple of times when I saw how AI technology could dramatically help:

* I worked with a research team inside X at Google who built models (in the pre-LLM/transformer days) that could help the highly skilled SWEs keep up with the constantly evolving monorepo. This was often very boring work, ripe for a computer to help.
* I worked on a project at Shopify that uses LLMs to bridge the complexity of GraphQL for developers wanting to integrate with merchant data. This quickly taught me lessons, such as:
    * It's easy to show a cool (*somewhat contrived*) demo
    * It's hard to build something great that works at scale in the real world
    * One LLM isn't the answer for all use cases
    * It's not just quantity... quality data matters
    * Having a system that can really do well wrt evaluations is vital as you iterate

Projects like these gave me the evidence to see how software engineering is going to radically change in the future, and pairing AI technology with developers will be the driver.

## Meeting the Augment team

I was sold on the opportunity that this AI wave could allow us to help developers in new expansive ways. I started to explore, and this exploration lead me to chatting with a couple old friends, [**Luke Wroblewski**](https://x.com/LukeW) and [**Sam Pullara**](https://x.com/sampullara) who are building companies at [**Sutter Hill Ventures**](https://shv.com/), a pretty unique VC firm.

Luke and Sam grinned as I spoke about my desire to build for developers with AI, and quickly introduced me to the founders and team behind Augment.

I met [**Guy Gur-Ari**](/company), the co-founder leading the research efforts at Augment. He had already assembled a team of AI researchers and engineers who had many years of expertise with ML and how it can be **applied** to code. This was important to me, as I had found that to build something truly great, you need the ability to make changes across the entire stack. You want to be able to change the engine along with the other parts of the car!

[**Igor Ostrovsky**](/company), the other co-founder and pioneer of Augment, also gave me a lot of faith that we had the broad technical expertise to pull this off at scale. His proven track record with distributed systems as Chief Architect of Pure Storage, developer focused work at Microsoft, and his deep dive into AI as an entrepreneur in residence with SHV was inspiring.

Then I discovered that [**Scott Dietzen had joined as CEO**](https://www.augmentcode.com/blog/reimagining-software-engineering-through-ai). I first met Scott at the birth of enterprise Java, where he was CTO at BEA WebLogic, my favorite app server of choice.

As I met the broader team, I had a strong feeling that this was a team with the focus, experience, and skill to take a shot at building the best AI platform and ecosystem for developers.

The team had gone deep in building foundational technology that is needed to solve the meaty problems that developers have, especially at scale. These include building a system that:

### Has an expert understanding of large codebases

There are solutions out there that feel like you have access to a system aware of core technology. They have a solid understanding of programming languages, and popular frameworks.

When using Augment, we want you to feel like you are working with the joint intuition of your most seasoned engineers at the company, and those with deep expertise on the dependencies that you use.

Any suggestions need to reflect the APIs and coding patterns in your company's code so your team can use it on your actual day-to-day work.

### Produces running code

The custom AI models and infrastructure are tuned for code and coding use cases avoiding frustrating hallucinations and focuses on improving code quality… not *just* productivity.

### Operates at the speed of thought

There were many search engines before Google, but I remember trying it for the first time, and seeing how the experience was a step change. The quality of the results were next level AND the speed to return them felt different.

Working with LLMs can be a lil... slow, which massively degrades the experience and can keep knocking you out of flow.

The team had built a fast inference — 3x faster than competitors — built on state-of-the-art techniques, including custom GPU kernels, and I felt the difference in the experience.

### Supports multiple developers & teams

Software development is a team sport. There are so many areas where technology can help scale and improve the use of best practices across a team, help you learn a complex codebase, and get new engineers onboarded faster.

The scale of computers allow a system to attend to do much more, and they are available 24x7.

I have learned the power of small teams. We have seen with early customers that the shape of teams can change when you deliver the right capabilities. If we can enable smaller teams to do more, and for teams to do more in parallel, we result in better software and happier devs to boot!

### Includes strong IP protections

Your company's source code is precious. Augment was designed from the first line of code for tenant isolation, with an architecture built to protect your IP.

## Try Augment Code

Joining Augment has already been a blast. Moving at startup speed with a great crew all focused on helping developers is a dream come true for me. I feel very fortunate to have the opportunity to go after this problem space with a small (but growing! [**Join us?**](https://www.augmentcode.com/careers)) team.

We are heads down delivering on this promise, working closely with early access customers, who have been a key part of our product development thanks to their fantastic feedback (thank you!).

If _you_ are interested in kicking the tires, [**please sign up for the waitlist!**](https://www.augmentcode.com/waitlist)

---

**About the Author:**  
Dion is passionate about helping developers succeed. At Google, Dion built products used by millions of developers across Chrome, Search, and Android. Dion was previously VP of Developer Experience at Shopify. Prior to that, he worked at Mozilla, Walmart, Palm, and led his own startups.
