# Introducing Next Edit: AI that understands the ripple effect of code changes

**Authors: <AUTHORS>
**Date:** February 19, 2025

Every software engineer knows the pain: you update a field in one file, and now you're hunting through the codebase updating SQL queries, tests, and type definitions to match. What should be a simple change becomes a tedious game of find-and-replace.

**Next Edit** is our solution to this problem. It extends code beyond the cursor by understanding the ripple effects of your changes and automatically suggesting updates across your entire workspace. While you code, it's scanning your codebase, identifying dependent files, and generating contextual suggestions that keep your code in sync. Next Edit is available today to everyone using Visual Studio Code, just update your extension and let Next Edit help you get more done.

## **A motivating example: Adding a new `session_id` field**

![Adding a session_id field](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67b618608a231b96dda40fd7_AD_4nXe8YB3nDVAPrkl7tGOm5ivsQWa114jqRHpuDgkG26lh6lHdBb6p0pm84CLk9LINuvcPLwk1bKdpozSnHuVHdOa_Y129SCMcI08JmUJdOabYxrYur7NgV4CRwr0TFEWaG39vbRrkeg.gif)

Imagine you're adding a new `session_id` field to an existing data class in your codebase. Traditionally, this would involve several manual steps:

1. **Updating the Data Class**: First, you'll need to add the new `session_id` field to your data class, placing it next to the existing request\_id field.
2. **Propagating Changes**: Then, you'll need to update all direct usages of that data class throughout your codebase. This includes modifying function calls, adjusting method signatures, and updating any place where the data class is instantiated or manipulated.
3. **Updating Related Components**: Beyond direct usages, you also need to modify relevant SQL queries, update protobuf messages, and adjust other related classes to incorporate the new `session_id` logic.

With **Next Edit**, as soon as you make the initial change to add the `session_id` field, Augment detects your intent and begins to suggest the necessary changes across your entire codebase.

## **Identifying and Updating Dependencies**

![Updating dependencies](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67b618613ffd7b90eccec2d0_AD_4nXeFrY56R0oI_PUAHtzj1z2vvjXB4o2CHxRlLzyDi6Lw1dNgN1FDNfYsdKCsjTkwlpGmBBV9trP-pHOMwY7yFdTqmYfeJrK7-PzE9JRV9ssWwWfpyP_Pc8xoypr7btaL8UuRvMvRPw.gif)

Once you introduce the `session_id` field, Next Edit systematically analyzes your codebase to ensure consistency across all relevant areas:

* **Direct Usages**: It identifies all the places where the data class is used and suggests edits to include the new `session_id` field.
* **SQL Queries**: It locates relevant SQL queries that need updating and provides suggested edits.
* **Related Classes**: It recognizes other classes and components that should incorporate the `session_id` logic, suggesting modifications to ensure consistency.

## **Ensuring Test Coverage**

![Updating tests](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67b61860f6d44a93f17279fa_AD_4nXf4o4lkgIfctSkzwjkYzOi1B4HJqVGRCCvE03IJ59TvPWosGywbMblQ8uen7JgiHDkAeZK5Fw_MVumXv6DrVMWOIl8tviGGeyUD9bJ2MCt8zkTfztrAGovkiwPP7IgWU2u3q0t-.gif)

To maintain correctness and prevent regressions, Next Edit also updates relevant tests and protobuf usages, ensuring they align with the new `session_id` field.

## **Beyond Refactoring: Supporting Iterative Development**

![Iterative development](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67b61860c2a590944c774448_AD_4nXccGEfvxJbw_PBY7qWIhrtIwj8wRh-PiBL5AQbcV9tFOfw5amSRnl9BgLOTrywxRghS1doPK0T4S68dNLz0ZKIeL0r0lx832DR-FyZH20K1KssEMUNhtc1jXdFrbCO_On2kiu_7pw.gif)

In addition to refactoring, we've also found this feature to be incredibly helpful for developing new code, as you iterate over different draft versions. Whether you're adjusting a function signature, renaming a variable, or refining logic, Next Edit automatically kicks in to update relevant parts of your codebase. This allows you to focus on problem-solving rather than tedious updates—so your workflow matches the speed of your thinking, not just your typing

## **How Next Edit is different**

While Augment Next Edit and other features like Cursor Tab or GitHub Copilot Next Edit Suggestions all propose changes beyond the cursor, Next Edit stands out in a few key ways. It relies on a specially trained location model and draws from your recent editing history to offer relevant suggestions—even in large files or across multiple files. By contrast, other tools often require manually positioning the cursor to trigger edits. Next Edit also leverages Augment's incredible codebase [retrieval](https://www.augmentcode.com/blog/a-real-time-index-for-your-codebase-secure-personal-scalable), delivering more accurate and context-aware edits overall.

### **Example: Augment Next Edit vs. Cursor Tab**

![Augment Next Edit](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67b663ebff69bd45d6464883_AD_4nXdL13Ac7hIiJaA0sLLNpDQSQNzYeTGI3nLJc8DPK4Sy3raxoOuZbmi5A9c2uPi1suUKuL7nay0rGmod8mbFl7548Ue5IQ7KVHM-2zDQ7Q3OI5mH_x6FtAtA5w2cgIZkbvNRgDj2ag.gif)

**Augment Next Edit:** Once `session_id` is added as a field in `EditDatum`, Next Edit finds and fixes all the changes in `edit_dataset.py` without the user needing to move their cursor to each relevant area!

![Cursor Tab](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67b663ebbbf7fe75d952b766_AD_4nXcPzW0HxWg_JP4GPFi9VTQmEH-dVldzY9UFr4Ewjg3cIQ2iWkuwdc3jDYt1bMeYmwNfKSi5CjgW9ZWeGsjMLaghMUVEyj5kInbG_fie0PYOMWLwYOoUpXO29RfFQy1F5IFXnj9XwQ.gif)

**Cursor Tab:** Needs a lot more intervention to make the changes in `edit_dataset.py` and the user has to move their cursor to relevant parts of the code to get suggestions.

## **We're not done: Further opportunities to improve Next Edit**

While we're excited about what we've achieved with Next Edit, we're committed to continuous improvement. Here's what's next: 

### **Automating Larger Commits and PRs**

We're working on scaling up Next Edit to handle more substantial changes:

* **From Small Commits to Big PRs**: Enhancing the model's ability to understand broader contexts and dependencies will allow it to assist with larger pull requests and significant refactoring tasks.
* **Understanding Complex Dependencies**: Improving the model's capability to comprehend and navigate complex code dependencies across multiple files and modules.
* **Bulk Edit Support**: Enabling the feature to suggest and apply changes across many files simultaneously and consistently, reducing the time spent on repetitive tasks.

### **Better Integration with Other Features Like Chat**

Integrating Next Edit with our chat functionality opens up new possibilities:

* **Enhanced Contextual Understanding**: Chat can provide additional context to make next edit suggestions more precise, especially when multiple potential edits are possible.
* **Interactive Problem Solving**: Developers can use chat to ask for explanations, request alternative solutions, or clarify the intent behind suggestions.
* **Cohesive Development Environment**: Combining these features creates a more intelligent and supportive environment, further reducing friction and enhancing productivity.

## **Conclusion**

Building Next Edit was a challenging but rewarding journey. We [tackled complex AI problems](https://www.augmentcode.com/blog/the-ai-research-behind-next-edit) to create a feature that understands your intent, knows where to apply changes, and executes those changes accurately and efficiently. By leveraging our [advanced retrieval infrastructure](https://www.augmentcode.com/blog/a-real-time-index-for-your-codebase-secure-personal-scalable), novel modeling techniques, and a deep understanding of developer workflows, we've created a tool that keeps you in the flow, reduces manual toil, and accelerates your coding process.

We're not stopping here. We're committed to continuously refining the user experience, expanding the feature's capabilities, and integrating it more deeply with other tools like Chat. Our goal is to make Next Edit an indispensable part of your development toolkit.

_At Augment, we're redefining what's possible in developer AI. If you're interested in learning more about Next Edit or want to share your thoughts, feel free to reach out. Your feedback is invaluable as we continue to enhance and expand our features._

---

**About the Authors: <AUTHORS>
**Arun Chaganty** is a research scientist at Augment Computing, where he works on new AI features like Next Edit. Previously, he worked on conversational AI at Google Research, led the Conversations AI team at Square, Inc. and was Head of AI at Eloquent Labs. He graduated with a PhD in computer science at Stanford University, working with Percy Liang and the Stanford NLP group.

**Jiayi Wei** is a research scientist at Augment, specializing in enhancing model quality for the Code Completion and Next Edit features. He graduated with a PhD in Computer Science at the University of Texas at Austin, working with Isil Dillig and Greg Durrett on applying deep learning to emerging programming tasks such as [probabilistic type inference](https://openreview.net/forum?id=4TyNEhI2GdN) and [code auto-editing](https://openreview.net/forum?id=ALVwQjZRS8).
