# Meet Augment Agent: Your AI pair programmer that deeply understands your codebase, and learns as you work

**Author:** <PERSON>  
**Date:** April 2, 2025

Today, we're excited to announce **Augment Agent**. Our team has been hard at work building an Agent that can deliver high-quality code, from a brand new app to a monorepo with over 100k files. Augment [Agent is built to address the challenging reality of software engineering in large codebases.](http://www.augmentcode.com/blog/software-agents-you-can-trust) You can try it now by signing up for our 14-day free trial. Augment Agent is available for [VS Code](https://marketplace.visualstudio.com/items?itemName=augment.vscode-augment) and [JetBrains](https://plugins.jetbrains.com/plugin/24072-augment).

## The Power of Context - Memories & Tools

At Augment, we've built our Context Engine to deliver the right context to every AI interaction. This is what sets us apart from our competition and what our users love. And, we've proven our expertise in building powerful, performant agents with our [#1 leaderboard spot on SWE-bench-verified.](https://www.augmentcode.com/blog/1-open-source-agent-on-swe-bench-verified-by-combining-claude-3-7-and-o1)

We took context further with <PERSON> by introducing **Memories,** which automatically update as you work with the Agent and persist across conversations to constantly improve the code generated, solve your tasks faster, and match your code style and pattern. But there's even more context about your infrastructure, roadmaps, and priorities to bring together with your code.

To bring context that is scattered throughout the software development lifecycle, we have fully embraced [**MCP**](https://modelcontextprotocol.io/introduction), giving you access to a wide range of tools and systems. Our early adopters have been using Vercel, Cloudflare, and other infrastructure MCPs to add additional context, automate workflows, and debug production issues. But if you want our [Agent to play you a lo-fi playlist on Spotify](https://x.com/Copypastaa/status/1907195851342217356) when you start coding, it can do that, too.

MCPs are just getting started and we're looking forward to contributing to the community to improve them, but we wanted to provide a first-class experience to some of the most utilized tools. That's why **we built our own Native Tools.** We are launching with GitHub, Jira, Confluence, Notion, and Linear. Let us know what other integrations you want to see next!

## Industry-Leading Context Capacity

Our expanded context capacity (up to 200K tokens) allows our Agent to handle complex, codebase-context dependent tasks that cause competitors' solutions to fail. **With twice the context capacity of comparable tools**, we seamlessly process all the context from your tools, our context engine, agent memories and your prompt—enabling you to tackle increasingly challenging development scenarios with confidence.

## And More…

Augment Agent is packed with all the features you need to solve real problems in production-grade codebases:

* [**Code Checkpoints**](https://docs.augmentcode.com/using-augment/agent#checkpoints)**:** Provide peace of mind while our agent tackles your tasks. Checkpoints automatically track changes and enable easy rollbacks to maintain your preferred programming style.
* **Multi-Modal:** share screenshots, Figma files, and any kind of screenshots to help you fix bugs or implement your next UI elements.
* **Terminal commands:** Besides file search and editing code, let our Agent run commands in your terminal, like npm install, run dev, or just interact with Git.
* [**Auto Mode**](https://docs.augmentcode.com/using-augment/agent#agent-modes): For when you don't want to confirm all of the agent's actions

Working with AI agents is new for everyone. To help you get started, we've put together a guide to [our best practices for using AI coding agents](http://augmentcode.com/blog/best-practices-for-using-ai-coding-agents).

## Transparent Pricing

Pricing for AI agents is difficult. Both compute and LLM access are expensive (but getting cheaper every day), and there is very limited data on agent usage patterns. We heard complaints and frustrations about how other AI tools' pricing is not clear, and we want to make sure we get it right.

To help us learn more about how developers use Augment Agent, we're launching with unlimited Early Adopters pricing:

* **Community:** Free
* **Developer:** $19/month
* **Team:** $49/seat/month
* **Enterprise:** Contact us

We expect pricing to change in the future, and to add limits on requests for the Community and Developer plan tiers. Customers who need more Agent requests than they have in their plan each month will be able to add them for an additional fee.

If you have feedback, please let us know in [Discord](http://augmentcode.com/discord).

## What's Next?

This is just the beginning; expect more native tools, improved integrations with your terminal and IDEs, and the ability to work with multiple agents at once… 🤫

For now, we would love for you to [give Augment Agent a try](https://augmentcode.com//install-now) and [give us your feedback](http://augmentcode.com/discord).

Happy Coding!

*A very special* ***thank you*** *to the alpha testers who gave us invaluable Augment Agent feedback: 8b\_t, Adam Xu, AlaGARBAA, Alan, amogower, Aayush Duhan, bairanbokkeri, Benjamin Kitt, bramburn, Chris Calvelage, craigary, Dan9571, devoid.foreman371, Heng Wang, HammerHand, JohnDotOwl, Jurgen22, jtutanota, Kravis Lau, marcosgrimm, Marvin Bitterlich, matkoson, MihiroH, Mike Luigi, Natchathat Chayutthana, niepokonany\_yt, Peilun Dai, penumbrae, Phan Phuong Duy, PierrunoYT, Pree, revanth\_12, rob.888, Sandrino Escobar, Solid, Techfren, TheFancyWolf, Tharuka, TNT-Flo, vijay\_83626, visualjc, xcruciating, Yuhanawa, Zersya, and .kingmelk.*

---

**About the Author:**  
Guy co-founded Augment Code after a stint at Google where he led a research team that focused on understanding and improving deep learning systems. He holds a Ph.D in theoretical physics from the Weizmann Institute of Science.
