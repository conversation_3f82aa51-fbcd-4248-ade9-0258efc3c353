# Meet Augment Code: Developer AI for teams

**Date:** October 24, 2024

_TL:DR: Today we're unveiling the first in-depth look at Augment Code, and inviting you to sign up to try it for yourself. Augment is the first coding AI that deeply understands your software and that scales to large, complex codebases._ [_Teams can request a free trial here._](https://www.augmentcode.com/free-trial)

AI is changing how we write code, and it's happening fast. AI can now create simple apps, explain chunks of code, and spot mistakes in programs. But professional developers don't work on small projects or bits of code. If you've been part of a software team, you know that real-world software development involves:

* Struggling to use APIs written by others
* Joining a new team with little to no documentation
* Attempting a migration without fully understanding the dependencies
* Refactoring a complex package

Until now, no AI coding platform has specifically targeted the challenges software engineering teams face working with large complicated codebases.

## Augment gets your codebase

Every Augment feature is context aware. This means every suggestion, completion, and interaction reflects the components, APIs, and coding patterns in your codebase. Augment scales to large codebases, including big open source projects like Unreal Engine and Chromium, and is used across leading startups and Fortune 500 organizations. 

> _"One of the biggest pain points any software team has is context on how every part of the codebase works and how it all fits together. Augment Code's seamless integration and intuitive suggestions have led to a remarkable increase in productivity and team happiness. It was one of the fastest adopted tools within the team as compared to any coding assistant Collective has tried."  
> ‍_**_Chintan Shah, VP of Engineering at Collective_**

## How Augment Works

Augment is developer AI for teams that works as an IDE extension, starting with [VS Code](https://marketplace.visualstudio.com/items?itemName=augment.vscode-augment) or [JetBrains (IntelliJ, WebStorm, PyCharm, etc.)](https://plugins.jetbrains.com/plugin/24072-augment). Use Augment for:

**Chat**: Ask questions about a codebase, get help hunting down a bug, or start spec'ing out your new feature.

**Code Completions**: Blazing fast in-line code suggestions let you tab your way through the consumption of an internal API, writing tests, or using a third-party SDK. 

**Suggested Edits**: Changing one file often causes a cascade of related changes across the repository. Suggested Edits finds and proposes associated changes, helping you complete your task in record time. Early Augment users report writing some of their PRs almost in entirety just by chaining Suggested Edits together. 

**Augment in Slack**: An always-available Slackbot that answers questions, even when your team is offline. Have deep, detailed discussions about how a system works so you can stay unblocked. @Augment in conversations to save precious cycles instead of using dev resources. [Watch a two-minute video](https://www.youtube.com/watch?v=FWf0-voC-Q8) on how to chat with your code in Slack.

## Augment is the first AI coding platform that is uniquely built for teams.

### Augment is secure and private

[Augment achieved SOC 2 Type II](https://www.augmentcode.com/blog/augment-code-achieves-soc2-type-ii) earlier this year, and will never train AI models on customers' proprietary code. Customers are granted always-on IP protection, so they can trust code suggestions to not infringe on software use licensing. 

> _"Security and privacy is definitely one of the very key considerations when we choose a vendor. We went through a thorough process of SOC II compliance review with Augment through our security and IT team."  
> ‍_**Joe Luo, Director of Engineering, Newfront** 

### Built for team management and adoption

Professional teams need more than just an IDE extension. To make sure developers are getting value from Augment, team admins have access to detailed usage metrics, for a close-up look at adoption and completion acceptance rate. 

> _"What I really like about Augment is being able to get the data behind how the AI is influencing code completion when the developers are engaging with it,"  
> ‍_**Paul Dysko, VP of Engineering, Paystone**

Augment has a flexible, usage-based pricing model starting at $60 per active user per month. There are no named seats in Augment, so teams only pay for developers who use it. 

> _"Augment's context-aware suggestions have definitely boosted our team's productivity - even for folks previously using tools such as GitHub Copilot. Augment is a clear step up from other AI coding assistants and our developers have admitted that they would personally buy Augment licenses if we didn't provide it for them. That's when you know a tool is truly valuable."  
> ‍_**Chris Johnson, VP Technology Delivery, Lineage**

## What our first customers are saying

> _"The fact that Augment doesn't make you think about context means you can ask questions about unknown unknowns and get really insightful answers back."  
> ‍_**_Merrick Christensen, Principal Engineer at Webflow._**

> _"Augment actually knows your code base. You don't have to accept the auto-completion only to later erase half of it."  
> ‍_**_Jan Baryła, Engineering Manager at Handoff._**

> _"I was super blown away because it allows you to say, 'Hey, Augment, where is this feature implemented?' And it can surface the frontend code, the backend code, and also the SQL migration."  
> ‍_**Christophe Plat, Engineering Manager, Pigment**

## The Augment mission

Augment was founded in 2022 to improve software development at scale using AI. Augment's engineering team boasts deep machine learning and systems expertise, with alumni from Google, Meta, NVIDIA, Microsoft, Databricks, Snowflake, VMware and Pure Storage. Earlier this year we announced our [$227M Series B](https://www.augmentcode.com/blog/augment-inc-raises-227-million) at a $977M valuation, backed by Sutter Hill Ventures, Index Ventures, Lightspeed Venture Partners, Innovation Endeavors and Meritech Capital. 

## Request a free trial for your team

Teams at Webflow, Kong, Pigment, and more are already building with Augment. The best way to see if Augment works for your team is to try it, for free. Give us your largest repos and most complex code to see what we can do. [Learn more and sign up here.](https://www.augmentcode.com/free-trial)
