# Personal journey: Reimagining software engineering through AI

**Author:** <PERSON>  
**Date:** August 15, 2024

Many moons ago, I had the good fortune to be accepted into Carnegie Mellon University and was drawn into computer science by the quality and enthusiasm of the faculty. 2018 Turing Award winner <PERSON> taught my intro to AI class, and I had a front row seat for the debates between the proponents of symbolic reasoning and neural nets (which at that time was really just <PERSON>). I ended up spending a decade at CMU collecting degrees, ultimately writing a dissertation on a programming language theoretic approach to machine learning, albeit in the good ol' fashioned symbolic reasoning end of AI.

I was a prolific programmer in university, sometimes even taking a couple of beers with me to the computer lab on a weekend evening. Since finally graduating, I have been an entrepreneur – Augment is the fifth startup that has agreed to take me on. Success in each of those endeavors has depended on brilliant engineers crafting best in class software. One would think that supremely talented teams enjoying significant success would be huge fun. And while there were many satisfying moments along those journeys, I continue to be struck by how that joy of programming solo I experienced fails to translate to far more talented teams building far more meaningful software. Why? Shouldn't doing great work with great people prove even more fun?

Instead, software teams continue to be plagued by, just for example:

* Too much drudgery sorting mundane code & tests;
* Too much time trying to understand code;
* Too much toil in updating libraries and dependencies;
* Too much effort required for code reuse and refactoring;
* Too much time doing code reviews and ramping newbies; and
* Too many meetings trying to coordinate efforts.

Most of all, engineers are hampered by the often uneven quality of the code they work on. Despite humankind investing north of $1 trillion annually in software engineering salaries, the large majority of software still disappoints – too complicated, too fragile, too slow, insecure, incomplete, etc. Software is either very expensive to maintain, or it becomes ossified as legacy once that investment lapses. Software cannot eat the world if it's of insufficient quality - human progress is in no small part gated on our breaking these bottlenecks in software quality and software engineering productivity.

Enter Large Language Models (LLMs). Like so many others, I was surprised by the innovation spurred by transformers and the power of Generative AI – that we could extend the simple prediction of the next token to such diverse applications of machine learning (ML). The obvious next question to me was could we leverage ML to improve software and programming at scale?

At the time I started thinking about these questions, I was "on the beach", having enjoyed a fabulous run at Pure Storage, helping to scale the business from 15 to nearly 2000 employees, from 0 to $1B in revenue, through a successful IPO, and then recruiting someone with far more scale experience to take the reigns. Sensing a kindred spirit, the early Augurs invited me to join in their research sessions that would culminate in Augment, Inc. and the product we will launch in the months to come (please stay tuned or join our early access program). I was even more thrilled to join the team in early '23. (I think I may lower the average IQ at Augment even more than I have in prior companies 😉).

Everything that has happened since then has reaffirmed that leap of faith. While current tools like GitHub CoPilot and ChatGPT do help individual programmers, they do not address the complexity of programming at scale: While they are modestly proficient in programming, they are unable to take advantage of the knowledge represented within your existing codebase - they function more like a fresh C.S. grad than a programmer steeped in your environment; While they indeed look over the shoulder of an individual programmer, they are unaware that the programmer is part of a broader team; and While they make it easy to add code, they do not sufficiently help reuse code, so your software gets more bloated, more complex, and more fragile. I dream of an AI that deletes code!

The next decade will see the biggest leap forward in software quality and team productivity since the advent of high-level languages, as well as AIs capable of ever deeper reasoning that restore the joy of crafting software. We hope you will join Augment on this journey.

---

**About the Author:**  
Prior to Augment Code, "Dietz" served as CEO of Pure Storage. Under his leadership, Pure grew from 0 to $1B+ in revenue, 15 to thousands of employees, and had a successful IPO. A 4x entrepreneur, he earned a Ph.D. in Computer Science with an emphasis on Machine Learning from Carnegie Mellon University.
