# To fork or not to fork?

**Author:** <PERSON>  
**Date:** March 7, 2025

Most of the competition Augment Code sees today across thousands of customer engagements is with GitHub Copilot, Cursor and Windsurf from Codeium. Each of those products works inside of the programmer's integrated development environment (IDE), but two different architectural approaches were taken: 

* Copilot and Augment are IDE plug-ins that work within industry standard IDEs like Microsoft's VS Code, the JetBrains family (IntelliJ, PyCharm, WebStorm, CLion, etc.) and VIM. 
* Cursor and Codeium with Windsurf instead chose to modify the source code of VS Code to tightly integrate that IDE with their AI capabilities. 

There is no question that building a standard plug-in that delivers a great user experience is hard work. It has taken Augment longer to perfect our user experience, and as a result Cursor and Windsurf have a head start in the market. At the same time, we think we are now better able to look after our customers' long-term needs. 

Of course software being open source means that every builder has the right to fork the code to take it in a direction of their choosing, but doing so comes with a current and future cost for the end-users:  

* **_Developers are forced to switch IDEs_** — For existing VS Code users, the current leap to Cursor or Windsurf is not a big change, but one would expect the gap to grow over time. And for dedicated JetBrains developers, it is a major sacrifice to lose the tooling they've used for years to have to embrace the wholly new IDE experience.  
      
* **_Loss of Microsoft support_** — Microsoft of course supports the VS Code IDE, but not the forks thereof (they after all generally don't have access to the source code for alternative versions). So Cursor and Windsurf users must get their IDE support from their new provider.

![Error message when using Microsoft extensions in VS Code forks](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67e45e68b7ee19111e1eb641_CleanShot%202025-03-07%20at%2012.42.04%402x.webp)

Expect errors when using Microsoft extensions in VS Code forks.

* **_Loss of compatibility with the Microsoft Ecosystem_** — Microsoft provides [numerous](https://marketplace.visualstudio.com/items?itemName=ms-python.python) [VS](https://marketplace.visualstudio.com/items?itemName=ms-vscode.cpptools) [Code](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-docker) [plug-ins](https://marketplace.visualstudio.com/items?itemName=ms-toolsai.jupyter) that they license, certify and support on their version of VS Code. Those plug-ins will be increasingly less likely to function as the Cursor and Windsurf forks continue to diverge from Microsoft's codebase. And even if they do work, Microsoft cannot be expected to support or license them on forked software they do not have access to or control over. (Of course, Cursor and Codeium may be able to build alternative versions of those plug-ins if the underlying Microsoft services support such access.)

* **_Loss of Microsoft's VS Code Community_** — Microsoft has invested in building a marketplace for VS Code plug-ins. Cursor and Windsurf are, of course, free to build their own such marketplaces or support the open source alternative, but such fragmentation will increase costs for participating vendors. Given this, it makes sense for Cursor and Windsurf customers to ensure that the set of VS Code plug-ins they count on will be tested and certified with their respective IDEs.  
      
* **_Access to future VS Code enhancements from Microsoft_** — Microsoft will continue to aggressively invest in VS Code, but Cursor and Windsurf users will receive those new features late or not at all depending on whether their owners want to do the work to port and test the relevant code to Cursor or Windsurf, respectively. Of course, Cursor and Windsurf will have their own roadmaps for their versions of the IDE (that is after all why you fork in the first place) that their customers may find more compelling than Microsoft's. 

And that is why Augment decided **_not_** to fork VS Code. The decision to fork or not to fork is, of course, just a single dimension of the ramping competition for coding AIs. Augment welcomes that competition, as it drives innovation and customer value. We have found our sweet spot in delivering a far deeper understanding of your software — where our competitor's AIs are capable of "0 to 1" starter projects, Augment shines supporting teams of software engineers looking after larger, more complex codebases.

As evidence that we're doing that uniquely well, it is funny that currently our third most popular and fastest growing IDE for trialing Augment is … Cursor ;-). 

![Cursor popularity chart](https://cdn.prod.website-files.com/66e230b8c8062a18d04d3723/67e45ebb8a1630344307b339_cursor_pop.webp)

The #3 most popular IDE on Augment? Cursor

---

**About the Author:**  
Prior to Augment Code, "Dietz" served as CEO of Pure Storage. Under his leadership, Pure grew from 0 to $1B+ in revenue, 15 to thousands of employees, and had a successful IPO. A 4x entrepreneur, he earned a Ph.D. in Computer Science with an emphasis on Machine Learning from Carnegie Mellon University.
