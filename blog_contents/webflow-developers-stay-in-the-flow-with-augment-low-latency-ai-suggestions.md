# Webflow developers stay in the flow with Augment's context-aware AI suggestions

**Author:** <PERSON><PERSON><PERSON>  
**Date:** October 15, 2024

> "We are seeing a greater return on our investment in Augment compared to other AIs we've used for code: more PRs per engineer, more code submissions, more bug fixes, and more tests written. Our new hires onboard with Augment to quickly get up to speed on how we build software at Webflow, ultimately helping us deliver value to our customers faster." <PERSON>, CTO, Webflow

## The Challenge

Webflow, a leading website experience platform, operates with a complex tech stack rooted in the JavaScript ecosystem.  
  
Webflow faced challenges in maximizing developer productivity and getting developers up to speed on various part of their codebase, particularly during onboarding. Specialized frontend and backend teams struggled to navigate their extensive codebase efficiently.

> _"Our tech stack is fairly complex. Maintaining consistency and scaling our development efforts was becoming increasingly difficult,"_ said **<PERSON><PERSON><PERSON>, Director of Engineering at Webflow.

## Why Augment?

Webflow sought a context-aware AI assistant to boost developer productivity and streamline onboarding. They chose Augment Code for several key reasons:

* **Deep Context Awareness:** Augment goes beyond file-level understanding, grasping relationships across the entire codebase. This allows developers to ask questions without needing to provide explicit context. _"The fact that Augment doesn't make you think about context means you can ask questions about unknown unknowns and get really insightful answers back,"_ shared **<PERSON><PERSON>, Principal Engineer at Webflow.
* **Prompt Engineering:** The team leveraged Augment to fine-tune complex tasks. **Huda Syed**, Engineering Manager at Webflow, added "_One aha moment that I've had is learning how to prompt engineer, and getting the most value out of Augment chat's output."_ 
* **Maintaining Flow State:** Developers can stay focused without distractions, thanks to Augment's quick responses and intuitive interface. _"The latency associated with prompts and edits... it's so quick these days that you're not distracted. So, the flow state is retained."_ Suchitra emphasized.
* **User-Centric Design:** Features like the diff view for suggested edits make the tool intuitive and efficient. _"The diff view was phenomenal... It's so intuitive. I know whether I want to accept or reject,"_ said Suchitra.
* **Sophisticated Tooling:** Compared to other AI coding assistants like GitHub Copilot, Augment offers more sophisticated insights and better context awareness. _"If the team is used to tooling like GitHub Copilot, then I think they're just going to have a more sophisticated tool to work with. It's a tool that's going to give them a lot more insight than Copilot was ever able to do,"_ Huda explained.

## The Results

Implementing Augment led to immediate benefits for Webflow's development team:

* **Improved Developer Productivity:** Automating routine tasks allowed developers to focus on high-impact work. _"It's gotten rid of a lot of the drudgery... It feels like having a pair-programmer that you're working with,"_ said Merrick.
* **Reduced Onboarding Time:** New engineers could self-serve answers to their questions, minimizing dependencies on senior team members. _"It improves the onboarding experience a lot... You're able to have these ego-less questions,"_ Merrick noted.
* **Enhanced Collaboration:** The AI assistant reduced the need for constant back-and-forth between team members. Huda adds, _"It can provide a solution to that potential error... that's kind of a new niche area to explore with this tool as well."_

## About Webflow

Webflow is a leading website experience platform based in San Francisco, California. It enables designers and developers to build professional, custom websites in a completely visual canvas without writing code. Combining the power of HTML, CSS, and JavaScript, Webflow empowers users to create responsive websites with ease.

**Industry**: Software Development

**Location**: San Francisco, California

**IDE**: IntelliJ, VS Code

---

**About the Author:**  
Molisha Shah is GTM and Customer Champion at Augment Code.
