{"functions": [{"description": "Search the web for real-time text and image responses. For example, you can get up-to-date information that might not be available in your training data, verify current facts, or find images that you can use in your project. You will see the text and images in the response. You can use the images by using the links in the <img> tag. Use this tool to find images you can use in your project. For example, if you need a logo, use this tool to find a logo.", "name": "web_search", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"fetch_content": {"default": false, "description": "Whether to crawl and include the content of each search result.", "type": "boolean"}, "search_term": {"description": "The search term to look up on the web. Be specific and include relevant keywords for better results. For technical queries, include version numbers or dates if relevant.", "type": "string"}, "type": {"default": "text", "description": "The type of search to perform (text or images)", "enum": ["text", "images"], "type": "string"}}, "required": ["search_term"], "type": "object"}}, {"description": "Scrape a web page to see its design and content. Use this tool to get a website's screenshot, title, description, and content. This is particularly useful when you need to clone the UI of a website. When using this tool, say \"I'll visit {url}...\" and never say \"I'll scrape\".", "name": "web_scrape", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"include_screenshot": {"default": false, "description": "Whether to include the screenshot of the web page in the response.", "type": "boolean"}, "theme": {"default": "light", "description": "To scrape the web page in light or dark mode.", "enum": ["light", "dark"], "type": "string"}, "url": {"description": "The URL of the web page to scrape. Must be a valid URL starting with http:// or https://", "format": "uri", "type": "string"}, "viewport": {"default": "desktop", "description": "The viewport to scrape the web page in.", "enum": ["mobile", "tablet", "desktop"], "type": "string"}}, "required": ["url"], "type": "object"}}, {"description": "Shortcut to create a new web project from a framework template. Each is configured with TypeScript, Biome, and Bun. Choose the best framework for the project.", "name": "startup", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"framework": {"default": "nextjs-shadcn", "enum": ["html-ts-css", "vue-vite", "react-vite", "react-vite-tailwind", "react-vite-shadcn", "nextjs-shadcn"], "type": "string"}, "project_name": {"default": "my-app", "pattern": "^[a-z0-9-]+$", "type": "string"}, "shadcnTheme": {"default": "zinc", "description": "The theme to use for the project. Choose zinc unless the app's requirements specify otherwise.", "enum": ["zinc", "blue", "green", "orange", "red", "rose", "violet", "yellow"], "type": "string"}}, "type": "object"}}, {"description": "Run a terminal command. Each command runs in a new shell.\nIMPORTANT: Do not use this tool to edit files. Use the `edit_file` tool instead.", "name": "run_terminal_cmd", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"command": {"description": "The terminal command to execute.", "type": "string"}, "project_information": {"additionalProperties": false, "description": "If the terminal `command` creates a new project or directory (for example, a Vite project through `bun create vite` or a new directory through `mkdir`), you must include the directory, install command, start command, and build command of the new project.", "properties": {"build_command": {"description": "Project build command", "type": "string"}, "directory": {"description": "Project directory", "type": "string"}, "install_command": {"description": "Project install command", "type": "string"}, "start_command": {"description": "Project start command", "type": "string"}}, "required": ["directory", "install_command", "start_command", "build_command"], "type": "object"}, "require_user_interaction": {"default": "", "description": "If the command requires user to interact with the terminal (for example, to install dependencies), write a notice to the user. A short single sentence starting with \"Interact with the terminal to ...\"", "type": "string"}, "starting_server": {"default": false, "description": "Whether the command starts a server process.", "type": "boolean"}, "update_linter_results": {"default": false, "description": "Whether to update linter results after running the command. Useful after fixing dependencies.", "type": "boolean"}}, "required": ["command"], "type": "object"}}, {"description": "List the contents of a directory. The quick tool to use for discovery, before using more targeted tools like semantic search or file reading. Useful to try to understand the file structure before diving deeper into specific files. Can be used to explore the codebase.", "name": "list_dir", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"target_dir_path": {"description": "Directory path to list contents of.", "type": "string"}}, "required": ["target_dir_path"], "type": "object"}}, {"description": "Fast file search based on fuzzy matching against file path. Use if you know part of the file path but don't know where it's located exactly. Response will be capped to 10 results. Make your query more specific if need to filter results further.", "name": "file_search", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"query": {"description": "Fuzzy filename to search for.", "type": "string"}}, "required": ["query"], "type": "object"}}, {"description": "Fast text-based regex search that finds exact pattern matches within files or directories, utilizing the ripgrep command for efficient searching. Results will be formatted in the style of ripgrep and can be configured to include line numbers and content. To avoid overwhelming output, the results are capped at 50 matches. Use the include or exclude patterns to filter the search scope by file type or specific paths. This is best for finding exact text matches or regex patterns. More precise than semantic search for finding specific strings or patterns. This is preferred over semantic search when we know the exact symbol/function name/etc. to search in some set of directories/file types.", "name": "grep_search", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"case_sensitive": {"description": "Whether the search should be case sensitive", "type": "boolean"}, "exclude_pattern": {"description": "Glob pattern for files to exclude", "type": "string"}, "include_pattern": {"description": "Glob pattern for files to include (e.g. '.ts' for TypeScript files)", "type": "string"}, "query": {"description": "The regex pattern to search for", "type": "string"}}, "required": ["query"], "type": "object"}}, {"description": "Read the contents of files. The output of this tool call will be the 1-indexed file contents from start_line_one_indexed to end_line_one_indexed_inclusive, together with a summary of the lines outside start_line_one_indexed and end_line_one_indexed_inclusive. Note that this call can view at most 250 lines at a time.\n\nWhen using this tool to gather information, it's your responsibility to ensure you have the COMPLETE context. Specifically, each time you call this command you should:\n1) Assess if the contents you viewed are sufficient to proceed with your task.\n2) Take note of where there are lines not shown.\n3) If the file contents you have viewed are insufficient, and you suspect they may be in lines not shown, call the tool again to view those lines.\n4) When in doubt, call this tool again. Remember that partial file views may miss critical dependencies, imports, or functionality.\n\nIn some cases, if reading a range of lines is not enough, you may choose to read the entire file. Use this option sparingly.", "name": "read_files", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"files_to_read": {"description": "A list of files to read.", "items": {"additionalProperties": false, "properties": {"end_line_one_indexed": {"default": 250, "description": "The one-indexed line number to end reading at (inclusive).", "type": "number"}, "should_read_entire_file": {"default": false, "description": "Whether to read the entire file. Defaults to false.", "type": "boolean"}, "start_line_one_indexed": {"default": 1, "description": "The one-indexed line number to start reading from (inclusive).", "type": "number"}, "target_file_path": {"description": "The path of the file to read.", "type": "string"}}, "required": ["target_file_path"], "type": "object"}, "type": "array"}}, "required": ["files_to_read"], "type": "object"}}, {"description": "Use this tool to make an edit to an existing file or create a new file. Specify the `target_file_path` argument first.\ncode_edit will be read by a less intelligent model, which will quickly apply the edit.\n\nYou should make it clear what the edit is while minimizing the unchanged code you write.\nWhen writing the edit, specify each edit in sequence using the special comment `// ... existing code ... <description of existing code>` to represent unchanged code in between edited lines.\n\nFor example:\n```\n// ... existing code ... <original import statements>\n<first edit here>\n// ... existing code ... <`LoginButton` component>\n<second edit here>\n// ... existing code ... <the rest of the file>\n```\nALWAYS include the `// ... existing code ... <description of existing code>` comment for each edit to indicate the code that should not be changed.\n\nYou should repeat as few lines of the original file as possible to convey the change.\nBut, each edit should contain sufficient context of unchanged lines around the code you are editing to resolve ambiguity.\nDO NOT omit spans of pre-existing code without using the `// ... existing code ... <description of existing code>` comment to indicate its absence.", "name": "edit_file", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"code_edit": {"description": "Specify <PERSON><PERSON>Y the precise lines of code that you wish to edit. **NEVER specify or write out unchanged code**. Instead, represent all unchanged code using the comment of the language you're editing in - example: `// ...[existing code] <description of existing code> ...`.", "type": "string"}, "instructions": {"description": "A single sentence instruction describing what you are going to do for the sketched edit. Don't repeat what you have said previously in normal messages. And use it to disambiguate uncertainty in the edit.", "type": "string"}, "smart_apply": {"default": false, "description": "Use a smart model to apply the code_edit. This is useful if the edit is long, or if the last edit was incorrect. Make sure to include the proper `// ... existing code ...` comments to indicate the code that should not be changed.", "type": "boolean"}, "target_file_path": {"description": "The target file to modify. The tool will create any directories in the path that don't exist.", "type": "string"}}, "required": ["target_file_path", "instructions", "code_edit"], "type": "object"}}, {"description": "Deletes multiple files or directories at the specified paths. Each operation will fail gracefully if:\n- The file doesn't exist\n- The operation is rejected for security reasons\n- The file cannot be deleted", "name": "delete_files", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"target_file_paths": {"description": "Array of file or directory paths to delete", "items": {"type": "string"}, "type": "array"}}, "required": ["target_file_paths"], "type": "object"}}, {"description": "Create a new version for a project. Calling this tool will automatically increment the version by 1. Make sure the app is error-free and implemented all of user's request before calling this tool.", "name": "versioning", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"version_changelog": {"description": "The version changelog. Write 1-5 short points.", "items": {"type": "string"}, "type": "array"}, "version_number": {"default": "", "description": "A whole number. Leave empty to automatically increment.", "type": "string"}, "version_title": {"description": "The title of the version. This is used to help the user navigate to the version.", "type": "string"}}, "required": ["version_title", "version_changelog"], "type": "object"}}, {"description": "Suggest 1-4 next steps that the user could take. Each step should be a clear, actionable prompt that the user could send. This is useful for guiding the user through a multi-step process or suggesting different directions they could take.", "name": "suggestions", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"suggestions": {"description": "List of 1-4 suggested next steps. No '-', bullet points, or other formatting.", "items": {"type": "string"}, "maxItems": 4, "minItems": 1, "type": "array"}}, "required": ["suggestions"], "type": "object"}}, {"description": "Update the project to the latest version before calling this tool. Deploys the project to Netlify. This tool will return one preview URL of the version and one main URL.\nNetlify accepts either static or dynamic site deployments. Deploying static sites is much faster. If the project have a database, backend, or api routes, deploy it as a dynamic site.\nIMPORTANT: To deploy a nextjs static site, read the `next.config.js` file and make sure it includes `output: 'export'` and `distDir: 'out'`.\nTo deploy a dynamic site, read the `netlify.toml` file and make sure the [build] section is set to the correct build command and output directory set in the project's `package.json` file. If your project uses remote images, write the `[images]` section in the file with remote_images set to an array of URLs that you want to use.\nDo not edit the `netlify.toml` file for static sites.\nIf deploying as a static site fails, try redeploying the project as a dynamic site.\nYou MUST ALWAYS use the exact parameters 'zip -rFS' (e.g. 'zip -rFS ../../output.zip .'). The -rFS flags are critical: they create a fresh archive, recursively include all files, and properly handle special cases.", "name": "deploy", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"deploy_as_dynamic_site": {"description": "Deploy as a dynamic site.", "type": "boolean"}, "deploy_as_static_site": {"additionalProperties": false, "description": "To deploy a static site. Write the build_and_zip_command and output_path.", "properties": {"build_and_zip_command": {"description": "A command to build the project and zip the output directory.", "type": "string"}, "output_path": {"description": "Path to the zip file to deploy.", "type": "string"}}, "required": ["build_and_zip_command", "output_path"], "type": "object"}}, "type": "object"}}]}